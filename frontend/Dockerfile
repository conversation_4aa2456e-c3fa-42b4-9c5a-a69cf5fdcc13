# 现代化多阶段构建 Dockerfile
FROM node:20-alpine AS base

# 安装必要的系统依赖
RUN apk add --no-cache \
    libc6-compat \
    dumb-init \
    && rm -rf /var/cache/apk/*

# 设置工作目录
WORKDIR /app

# 安装依赖阶段
FROM base AS deps

# 复制包管理文件
COPY package.json package-lock.json* ./

# 安装生产依赖
RUN \
  if [ -f package-lock.json ]; then \
    npm ci --only=production --frozen-lockfile && \
    npm cache clean --force; \
  else \
    echo "Lockfile not found." && exit 1; \
  fi

# 安装开发依赖（用于构建）
FROM base AS dev-deps
COPY package.json package-lock.json* ./
RUN \
  if [ -f package-lock.json ]; then \
    npm ci --frozen-lockfile && \
    npm cache clean --force; \
  else \
    echo "Lockfile not found." && exit 1; \
  fi

# 构建阶段
FROM base AS builder

# 复制开发依赖
COPY --from=dev-deps /app/node_modules ./node_modules

# 复制源代码
COPY . .

# 设置构建环境变量
ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV=production

# 构建应用
RUN npm run build

# 清理不必要的文件
RUN rm -rf node_modules && \
    rm -rf .git && \
    rm -rf .next/cache

# 生产运行阶段
FROM base AS runner

# 设置生产环境变量
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# 创建非root用户和组
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# 复制生产依赖
COPY --from=deps /app/node_modules ./node_modules

# 复制构建产物
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

# 创建必要的目录并设置权限
RUN mkdir -p .next && \
    chown -R nextjs:nodejs /app && \
    chmod -R 755 /app

# 切换到非root用户
USER nextjs

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node healthcheck.js

# 使用dumb-init作为PID 1
ENTRYPOINT ["dumb-init", "--"]

# 启动应用
CMD ["node", "server.js"]
