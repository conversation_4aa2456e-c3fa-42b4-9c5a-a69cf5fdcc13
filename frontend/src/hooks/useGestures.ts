"use client"

import { useRef, useCallback, useEffect } from "react"

export interface GestureHandlers {
  onTap?: () => void
  onDoubleTap?: () => void
  onLongPress?: () => void
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  onSwipeUp?: () => void
  onSwipeDown?: () => void
  onPinchStart?: () => void
  onPinchEnd?: () => void
  onPinch?: (scale: number) => void
}

export interface GestureOptions {
  longPressDelay?: number
  doubleTapDelay?: number
  swipeThreshold?: number
  pinchThreshold?: number
  disabled?: boolean
}

export function useGestures(
  handlers: GestureHandlers,
  options: GestureOptions = {}
) {
  const {
    longPressDelay = 500,
    doubleTapDelay = 300,
    swipeThreshold = 50,
    pinchThreshold = 0.1,
    disabled = false,
  } = options

  const touchStartRef = useRef<{ x: number; y: number; time: number } | null>(null)
  const touchEndRef = useRef<{ x: number; y: number; time: number } | null>(null)
  const longPressTimerRef = useRef<NodeJS.Timeout | null>(null)
  const lastTapRef = useRef<number>(0)
  const initialDistanceRef = useRef<number>(0)
  const isPinchingRef = useRef<boolean>(false)

  // 清理定时器
  const clearTimers = useCallback(() => {
    if (longPressTimerRef.current) {
      clearTimeout(longPressTimerRef.current)
      longPressTimerRef.current = null
    }
  }, [])

  // 计算两点距离
  const getDistance = useCallback((touch1: Touch, touch2: Touch) => {
    const dx = touch1.clientX - touch2.clientX
    const dy = touch1.clientY - touch2.clientY
    return Math.sqrt(dx * dx + dy * dy)
  }, [])

  // 触摸开始
  const handleTouchStart = useCallback((e: TouchEvent) => {
    if (disabled) return

    const touch = e.touches[0]
    const now = Date.now()

    if (e.touches.length === 1) {
      // 单指触摸
      touchStartRef.current = {
        x: touch.clientX,
        y: touch.clientY,
        time: now,
      }
      touchEndRef.current = null

      // 长按检测
      if (handlers.onLongPress) {
        longPressTimerRef.current = setTimeout(() => {
          handlers.onLongPress?.()
          // 触发触觉反馈
          if ('vibrate' in navigator) {
            navigator.vibrate(50)
          }
        }, longPressDelay)
      }
    } else if (e.touches.length === 2) {
      // 双指触摸 - 捏合手势
      clearTimers()
      const distance = getDistance(e.touches[0], e.touches[1])
      initialDistanceRef.current = distance
      isPinchingRef.current = true
      handlers.onPinchStart?.()
    }
  }, [disabled, handlers, longPressDelay, getDistance, clearTimers])

  // 触摸移动
  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (disabled) return

    if (e.touches.length === 1) {
      // 单指移动 - 取消长按
      clearTimers()
      
      const touch = e.touches[0]
      touchEndRef.current = {
        x: touch.clientX,
        y: touch.clientY,
        time: Date.now(),
      }
    } else if (e.touches.length === 2 && isPinchingRef.current) {
      // 双指移动 - 捏合手势
      const distance = getDistance(e.touches[0], e.touches[1])
      const scale = distance / initialDistanceRef.current
      
      if (Math.abs(scale - 1) > pinchThreshold) {
        handlers.onPinch?.(scale)
      }
    }
  }, [disabled, handlers, getDistance, pinchThreshold, clearTimers])

  // 触摸结束
  const handleTouchEnd = useCallback((e: TouchEvent) => {
    if (disabled) return

    clearTimers()

    if (isPinchingRef.current) {
      // 结束捏合手势
      isPinchingRef.current = false
      handlers.onPinchEnd?.()
      return
    }

    if (!touchStartRef.current) return

    const now = Date.now()
    const touchDuration = now - touchStartRef.current.time

    if (!touchEndRef.current) {
      // 简单点击
      const timeSinceLastTap = now - lastTapRef.current

      if (timeSinceLastTap < doubleTapDelay && handlers.onDoubleTap) {
        // 双击
        handlers.onDoubleTap()
        lastTapRef.current = 0
        // 触发触觉反馈
        if ('vibrate' in navigator) {
          navigator.vibrate([30, 10, 30])
        }
      } else if (handlers.onTap) {
        // 单击
        handlers.onTap()
        lastTapRef.current = now
        // 触发触觉反馈
        if ('vibrate' in navigator) {
          navigator.vibrate(10)
        }
      }
      return
    }

    // 滑动检测
    const deltaX = touchStartRef.current.x - touchEndRef.current.x
    const deltaY = touchStartRef.current.y - touchEndRef.current.y
    const absDeltaX = Math.abs(deltaX)
    const absDeltaY = Math.abs(deltaY)

    if (Math.max(absDeltaX, absDeltaY) > swipeThreshold) {
      if (absDeltaX > absDeltaY) {
        // 水平滑动
        if (deltaX > 0) {
          handlers.onSwipeLeft?.()
        } else {
          handlers.onSwipeRight?.()
        }
      } else {
        // 垂直滑动
        if (deltaY > 0) {
          handlers.onSwipeUp?.()
        } else {
          handlers.onSwipeDown?.()
        }
      }
      
      // 触发触觉反馈
      if ('vibrate' in navigator) {
        navigator.vibrate(20)
      }
    } else if (handlers.onTap && touchDuration < 200) {
      // 快速点击
      const timeSinceLastTap = now - lastTapRef.current

      if (timeSinceLastTap < doubleTapDelay && handlers.onDoubleTap) {
        handlers.onDoubleTap()
        lastTapRef.current = 0
      } else {
        handlers.onTap()
        lastTapRef.current = now
      }
    }

    touchStartRef.current = null
    touchEndRef.current = null
  }, [
    disabled,
    handlers,
    doubleTapDelay,
    swipeThreshold,
    clearTimers,
  ])

  // 绑定事件监听器
  const bindGestures = useCallback((element: HTMLElement | null) => {
    if (!element || disabled) return

    element.addEventListener('touchstart', handleTouchStart, { passive: false })
    element.addEventListener('touchmove', handleTouchMove, { passive: false })
    element.addEventListener('touchend', handleTouchEnd, { passive: false })

    return () => {
      element.removeEventListener('touchstart', handleTouchStart)
      element.removeEventListener('touchmove', handleTouchMove)
      element.removeEventListener('touchend', handleTouchEnd)
    }
  }, [disabled, handleTouchStart, handleTouchMove, handleTouchEnd])

  // 清理
  useEffect(() => {
    return () => {
      clearTimers()
    }
  }, [clearTimers])

  return {
    bindGestures,
    clearTimers,
  }
}

// 触觉反馈Hook
export function useHapticFeedback() {
  const triggerLight = useCallback(() => {
    if ('vibrate' in navigator) {
      navigator.vibrate(10)
    }
  }, [])

  const triggerMedium = useCallback(() => {
    if ('vibrate' in navigator) {
      navigator.vibrate(20)
    }
  }, [])

  const triggerHeavy = useCallback(() => {
    if ('vibrate' in navigator) {
      navigator.vibrate(50)
    }
  }, [])

  const triggerSuccess = useCallback(() => {
    if ('vibrate' in navigator) {
      navigator.vibrate([10, 10, 10])
    }
  }, [])

  const triggerError = useCallback(() => {
    if ('vibrate' in navigator) {
      navigator.vibrate([50, 50, 50])
    }
  }, [])

  const triggerWarning = useCallback(() => {
    if ('vibrate' in navigator) {
      navigator.vibrate([30, 10, 30])
    }
  }, [])

  return {
    triggerLight,
    triggerMedium,
    triggerHeavy,
    triggerSuccess,
    triggerError,
    triggerWarning,
  }
}

// 设备检测Hook
export function useDeviceDetection() {
  const isMobile = useCallback(() => {
    return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    )
  }, [])

  const isTablet = useCallback(() => {
    return /iPad|Android(?!.*Mobile)/i.test(navigator.userAgent)
  }, [])

  const isIOS = useCallback(() => {
    return /iPad|iPhone|iPod/.test(navigator.userAgent)
  }, [])

  const isAndroid = useCallback(() => {
    return /Android/i.test(navigator.userAgent)
  }, [])

  const isTouchDevice = useCallback(() => {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0
  }, [])

  const getScreenSize = useCallback(() => {
    return {
      width: window.innerWidth,
      height: window.innerHeight,
      orientation: window.innerWidth > window.innerHeight ? 'landscape' : 'portrait',
    }
  }, [])

  return {
    isMobile: isMobile(),
    isTablet: isTablet(),
    isIOS: isIOS(),
    isAndroid: isAndroid(),
    isTouchDevice: isTouchDevice(),
    getScreenSize,
  }
}
