"use client"

import * as React from "react"
import { motion, AnimatePresence, PanInfo } from "framer-motion"
import { 
  Menu, 
  X, 
  Home, 
  User, 
  Settings, 
  LogOut,
  ChevronRight,
  Shield
} from "lucide-react"
import { EnhancedGlassCard } from "@/components/ui/enhanced-glass"
import { GlassButton } from "@/components/ui/glass"
import { cn } from "@/lib/utils"

export interface MobileNavigationProps {
  isOpen: boolean
  onToggle: () => void
  onClose: () => void
  currentUser?: {
    username: string
    email?: string
    avatar?: string
  }
  onLogout?: () => void
  className?: string
}

export const MobileNavigation: React.FC<MobileNavigationProps> = ({
  isOpen,
  onToggle,
  onClose,
  currentUser,
  onLogout,
  className,
}) => {
  const [dragOffset, setDragOffset] = React.useState(0)

  const navigationItems = [
    { icon: Home, label: "首页", href: "/" },
    { icon: User, label: "个人中心", href: "/profile" },
    { icon: Settings, label: "设置", href: "/settings" },
    ...(currentUser?.username === "admin" ? [
      { icon: Shield, label: "管理后台", href: "/admin" }
    ] : []),
  ]

  const handleDragEnd = (event: any, info: PanInfo) => {
    const threshold = -100
    if (info.offset.x < threshold) {
      onClose()
    }
    setDragOffset(0)
  }

  const handleDrag = (event: any, info: PanInfo) => {
    if (info.offset.x < 0) {
      setDragOffset(info.offset.x)
    }
  }

  return (
    <>
      {/* 汉堡菜单按钮 */}
      <motion.button
        onClick={onToggle}
        className={cn(
          "fixed top-4 left-4 z-50 w-12 h-12 rounded-xl",
          "bg-white/10 backdrop-blur-[15px] border border-white/20",
          "flex items-center justify-center",
          "hover:bg-white/20 transition-all duration-300",
          "focus:outline-none focus:ring-2 focus:ring-primary/50",
          "lg:hidden", // 只在移动端显示
          className
        )}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <AnimatePresence mode="wait">
          {isOpen ? (
            <motion.div
              key="close"
              initial={{ rotate: -90, opacity: 0 }}
              animate={{ rotate: 0, opacity: 1 }}
              exit={{ rotate: 90, opacity: 0 }}
              transition={{ duration: 0.2 }}
            >
              <X className="w-6 h-6 text-white" />
            </motion.div>
          ) : (
            <motion.div
              key="menu"
              initial={{ rotate: 90, opacity: 0 }}
              animate={{ rotate: 0, opacity: 1 }}
              exit={{ rotate: -90, opacity: 0 }}
              transition={{ duration: 0.2 }}
            >
              <Menu className="w-6 h-6 text-white" />
            </motion.div>
          )}
        </AnimatePresence>
      </motion.button>

      {/* 导航抽屉 */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* 背景遮罩 */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
              onClick={onClose}
            />

            {/* 导航面板 */}
            <motion.div
              initial={{ x: "-100%" }}
              animate={{ x: dragOffset }}
              exit={{ x: "-100%" }}
              transition={{ 
                type: "spring", 
                damping: 25, 
                stiffness: 300,
                duration: 0.3 
              }}
              drag="x"
              dragConstraints={{ left: -300, right: 0 }}
              dragElastic={0.1}
              onDrag={handleDrag}
              onDragEnd={handleDragEnd}
              className="fixed top-0 left-0 h-full w-80 z-50 lg:hidden"
            >
              <EnhancedGlassCard
                variant="dark"
                size="none"
                className="h-full w-full rounded-none rounded-r-3xl p-0 overflow-hidden"
              >
                {/* 头部区域 */}
                <div className="p-6 border-b border-white/10">
                  {currentUser ? (
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-gradient-to-br from-primary/20 to-purple-500/20 rounded-xl flex items-center justify-center">
                        <span className="text-lg font-bold text-white">
                          {currentUser.username.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-white">
                          {currentUser.username}
                        </h3>
                        {currentUser.email && (
                          <p className="text-sm text-white/70">
                            {currentUser.email}
                          </p>
                        )}
                      </div>
                    </div>
                  ) : (
                    <div className="text-center">
                      <h3 className="text-lg font-semibold text-white mb-2">
                        欢迎使用
                      </h3>
                      <p className="text-sm text-white/70">
                        请登录以获得完整体验
                      </p>
                    </div>
                  )}
                </div>

                {/* 导航菜单 */}
                <div className="flex-1 p-4">
                  <nav className="space-y-2">
                    {navigationItems.map((item, index) => (
                      <motion.a
                        key={item.href}
                        href={item.href}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="flex items-center justify-between p-3 rounded-xl hover:bg-white/10 transition-colors duration-200 group"
                        onClick={onClose}
                      >
                        <div className="flex items-center space-x-3">
                          <item.icon className="w-5 h-5 text-white/80 group-hover:text-white transition-colors" />
                          <span className="text-white/80 group-hover:text-white transition-colors">
                            {item.label}
                          </span>
                        </div>
                        <ChevronRight className="w-4 h-4 text-white/50 group-hover:text-white/80 transition-colors" />
                      </motion.a>
                    ))}
                  </nav>
                </div>

                {/* 底部操作区域 */}
                {currentUser && (
                  <div className="p-4 border-t border-white/10">
                    <GlassButton
                      variant="subtle"
                      size="lg"
                      onClick={() => {
                        onLogout?.()
                        onClose()
                      }}
                      className="w-full justify-start hover:bg-red-500/20 border-red-400/30"
                      leftIcon={<LogOut className="w-5 h-5" />}
                    >
                      退出登录
                    </GlassButton>
                  </div>
                )}

                {/* 拖拽指示器 */}
                <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                  <div className="w-1 h-12 bg-white/20 rounded-full" />
                </div>
              </EnhancedGlassCard>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  )
}

// 移动端底部导航栏
export interface MobileBottomNavProps {
  currentPath?: string
  onNavigate?: (path: string) => void
  className?: string
}

export const MobileBottomNav: React.FC<MobileBottomNavProps> = ({
  currentPath = "/",
  onNavigate,
  className,
}) => {
  const navItems = [
    { icon: Home, label: "首页", path: "/" },
    { icon: User, label: "用户", path: "/profile" },
    { icon: Settings, label: "设置", path: "/settings" },
  ]

  return (
    <motion.div
      initial={{ y: 100 }}
      animate={{ y: 0 }}
      className={cn(
        "fixed bottom-0 left-0 right-0 z-40",
        "lg:hidden", // 只在移动端显示
        className
      )}
    >
      <EnhancedGlassCard
        variant="strong"
        size="none"
        className="mx-4 mb-4 rounded-2xl p-2"
      >
        <div className="flex items-center justify-around">
          {navItems.map((item) => {
            const isActive = currentPath === item.path
            
            return (
              <motion.button
                key={item.path}
                onClick={() => onNavigate?.(item.path)}
                className={cn(
                  "flex flex-col items-center justify-center p-3 rounded-xl transition-all duration-200",
                  isActive 
                    ? "bg-primary/20 text-primary" 
                    : "text-white/70 hover:text-white hover:bg-white/10"
                )}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <item.icon className="w-5 h-5 mb-1" />
                <span className="text-xs font-medium">{item.label}</span>
                
                {isActive && (
                  <motion.div
                    layoutId="activeTab"
                    className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary rounded-full"
                  />
                )}
              </motion.button>
            )
          })}
        </div>
      </EnhancedGlassCard>
    </motion.div>
  )
}

export default MobileNavigation
