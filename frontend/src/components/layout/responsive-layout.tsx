"use client"

import * as React from "react"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"

export interface ResponsiveLayoutProps {
  children: React.ReactNode
  className?: string
  maxWidth?: "sm" | "md" | "lg" | "xl" | "2xl" | "full"
  padding?: "none" | "sm" | "md" | "lg"
  centered?: boolean
}

const maxWidthClasses = {
  sm: "max-w-sm",
  md: "max-w-md", 
  lg: "max-w-lg",
  xl: "max-w-xl",
  "2xl": "max-w-2xl",
  full: "max-w-full"
}

const paddingClasses = {
  none: "p-0",
  sm: "p-4 sm:p-6",
  md: "p-4 sm:p-6 lg:p-8",
  lg: "p-6 sm:p-8 lg:p-12"
}

export function ResponsiveLayout({
  children,
  className,
  maxWidth = "lg",
  padding = "md",
  centered = true
}: ResponsiveLayoutProps) {
  return (
    <div
      className={cn(
        "w-full",
        maxWidthClasses[maxWidth],
        paddingClasses[padding],
        centered && "mx-auto",
        className
      )}
    >
      {children}
    </div>
  )
}

// 响应式网格组件
export interface ResponsiveGridProps {
  children: React.ReactNode
  className?: string
  cols?: {
    default?: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
  }
  gap?: "none" | "sm" | "md" | "lg" | "xl"
}

const gapClasses = {
  none: "gap-0",
  sm: "gap-2 sm:gap-3",
  md: "gap-3 sm:gap-4 lg:gap-6",
  lg: "gap-4 sm:gap-6 lg:gap-8",
  xl: "gap-6 sm:gap-8 lg:gap-12"
}

export function ResponsiveGrid({
  children,
  className,
  cols = { default: 1, sm: 1, md: 2, lg: 3 },
  gap = "md"
}: ResponsiveGridProps) {
  const gridClasses = React.useMemo(() => {
    const classes = ["grid"]
    
    if (cols.default) classes.push(`grid-cols-${cols.default}`)
    if (cols.sm) classes.push(`sm:grid-cols-${cols.sm}`)
    if (cols.md) classes.push(`md:grid-cols-${cols.md}`)
    if (cols.lg) classes.push(`lg:grid-cols-${cols.lg}`)
    if (cols.xl) classes.push(`xl:grid-cols-${cols.xl}`)
    
    return classes.join(" ")
  }, [cols])

  return (
    <div
      className={cn(
        gridClasses,
        gapClasses[gap],
        className
      )}
    >
      {children}
    </div>
  )
}

// 响应式堆栈组件
export interface ResponsiveStackProps {
  children: React.ReactNode
  className?: string
  direction?: {
    default?: "row" | "col"
    sm?: "row" | "col"
    md?: "row" | "col"
    lg?: "row" | "col"
  }
  spacing?: "none" | "sm" | "md" | "lg" | "xl"
  align?: "start" | "center" | "end" | "stretch"
  justify?: "start" | "center" | "end" | "between" | "around" | "evenly"
}

const spacingClasses = {
  none: "",
  sm: "space-y-2 space-x-2",
  md: "space-y-3 space-x-3 sm:space-y-4 sm:space-x-4",
  lg: "space-y-4 space-x-4 sm:space-y-6 sm:space-x-6",
  xl: "space-y-6 space-x-6 sm:space-y-8 sm:space-x-8"
}

const alignClasses = {
  start: "items-start",
  center: "items-center",
  end: "items-end",
  stretch: "items-stretch"
}

const justifyClasses = {
  start: "justify-start",
  center: "justify-center",
  end: "justify-end",
  between: "justify-between",
  around: "justify-around",
  evenly: "justify-evenly"
}

export function ResponsiveStack({
  children,
  className,
  direction = { default: "col", md: "row" },
  spacing = "md",
  align = "start",
  justify = "start"
}: ResponsiveStackProps) {
  const directionClasses = React.useMemo(() => {
    const classes = ["flex"]
    
    if (direction.default === "row") {
      classes.push("flex-row")
    } else {
      classes.push("flex-col")
    }
    
    if (direction.sm) {
      classes.push(direction.sm === "row" ? "sm:flex-row" : "sm:flex-col")
    }
    
    if (direction.md) {
      classes.push(direction.md === "row" ? "md:flex-row" : "md:flex-col")
    }
    
    if (direction.lg) {
      classes.push(direction.lg === "row" ? "lg:flex-row" : "lg:flex-col")
    }
    
    return classes.join(" ")
  }, [direction])

  return (
    <div
      className={cn(
        directionClasses,
        spacingClasses[spacing],
        alignClasses[align],
        justifyClasses[justify],
        className
      )}
    >
      {children}
    </div>
  )
}

// 响应式容器组件
export interface ResponsiveContainerProps {
  children: React.ReactNode
  className?: string
  fluid?: boolean
  breakpoint?: "sm" | "md" | "lg" | "xl" | "2xl"
}

const containerClasses = {
  sm: "container sm:max-w-sm",
  md: "container md:max-w-md",
  lg: "container lg:max-w-lg", 
  xl: "container xl:max-w-xl",
  "2xl": "container 2xl:max-w-2xl"
}

export function ResponsiveContainer({
  children,
  className,
  fluid = false,
  breakpoint = "lg"
}: ResponsiveContainerProps) {
  return (
    <div
      className={cn(
        fluid ? "w-full" : containerClasses[breakpoint],
        "mx-auto px-4 sm:px-6 lg:px-8",
        className
      )}
    >
      {children}
    </div>
  )
}

// 响应式显示/隐藏组件
export interface ResponsiveShowProps {
  children: React.ReactNode
  on?: ("sm" | "md" | "lg" | "xl")[]
  above?: "sm" | "md" | "lg" | "xl"
  below?: "sm" | "md" | "lg" | "xl"
  className?: string
}

export function ResponsiveShow({
  children,
  on,
  above,
  below,
  className
}: ResponsiveShowProps) {
  const visibilityClasses = React.useMemo(() => {
    const classes = ["block"]
    
    if (on) {
      classes.push("hidden")
      on.forEach(breakpoint => {
        classes.push(`${breakpoint}:block`)
      })
    }
    
    if (above) {
      classes.push("hidden", `${above}:block`)
    }
    
    if (below) {
      const breakpoints = ["sm", "md", "lg", "xl"]
      const index = breakpoints.indexOf(below)
      if (index > 0) {
        classes.push(`${breakpoints[index]}:hidden`)
      }
    }
    
    return classes.join(" ")
  }, [on, above, below])

  return (
    <div className={cn(visibilityClasses, className)}>
      {children}
    </div>
  )
}

// 响应式间距组件
export interface ResponsiveSpacerProps {
  size?: {
    default?: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
  }
  className?: string
}

export function ResponsiveSpacer({
  size = { default: 4, md: 6, lg: 8 },
  className
}: ResponsiveSpacerProps) {
  const spacerClasses = React.useMemo(() => {
    const classes = []
    
    if (size.default) classes.push(`h-${size.default}`)
    if (size.sm) classes.push(`sm:h-${size.sm}`)
    if (size.md) classes.push(`md:h-${size.md}`)
    if (size.lg) classes.push(`lg:h-${size.lg}`)
    if (size.xl) classes.push(`xl:h-${size.xl}`)
    
    return classes.join(" ")
  }, [size])

  return <div className={cn(spacerClasses, className)} />
}
