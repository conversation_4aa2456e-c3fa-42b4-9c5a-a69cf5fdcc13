"use client"

import * as React from "react"
import { motion } from "framer-motion"
import { 
  User, 
  LogOut, 
  ShoppingBag, 
  RefreshCw, 
  Plus,
  Info,
  Package,
  Clock,
  Tag,
  Users,
  ChevronRight
} from "lucide-react"
import { GlassCard, GlassButton } from "@/components/ui/glass"
import { EnhancedGlassCard } from "@/components/ui/enhanced-glass"
import { FadeIn, Stagger } from "@/components/ui/animated-elements"
import { useGestures, useHapticFeedback } from "@/hooks/useGestures"
import { formatDate, formatRemainingDays } from "@/lib/utils"
import type { User as UserType, OrderGroup } from "@/types"

export interface UserPanelProps {
  user: UserType
  orders: OrderGroup[]
  isLoadingOrders?: boolean
  onLogout: () => void
  onRefreshOrders: () => void
  onBindNewOrder: () => void
  onViewOrderDetails: (group: OrderGroup) => void
}

export function UserPanel({
  user,
  orders,
  isLoadingOrders = false,
  onLogout,
  onRefreshOrders,
  onBindNewOrder,
  onViewOrderDetails
}: UserPanelProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
      className="space-y-8"
    >
      {/* 用户信息头部 */}
      <div className="flex justify-between items-center">
        <div className="flex items-center space-x-4">
          <div className="w-12 h-12 glass rounded-xl flex items-center justify-center">
            <User className="w-6 h-6 text-white" />
          </div>
          <div>
            <h2 className="text-2xl font-semibold text-white">用户中心</h2>
            <p className="text-white/70">欢迎回来，{user.username}</p>
          </div>
        </div>
        <GlassButton
          variant="subtle"
          onClick={onLogout}
          leftIcon={<LogOut className="w-4 h-4" />}
        >
          退出
        </GlassButton>
      </div>

      {/* 账户信息卡片 */}
      <GlassCard
        variant="glass"
        hover="lift"
        className="p-6"
      >
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-white flex items-center">
            <Info className="w-5 h-5 mr-2" />
            账户信息
          </h3>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <div className="text-center">
            <p className="text-white/60 text-sm mb-1">用户名</p>
            <p className="text-white font-semibold">{user.username}</p>
          </div>
          <div className="text-center">
            <p className="text-white/60 text-sm mb-1">邮箱</p>
            <p className="text-white font-semibold">{user.email}</p>
          </div>
          <div className="text-center">
            <p className="text-white/60 text-sm mb-1">注册时间</p>
            <p className="text-white font-semibold">
              {user.registerTime ? formatDate(user.registerTime) : '未知'}
            </p>
          </div>
        </div>
      </GlassCard>

      {/* 订单列表卡片 */}
      <GlassCard
        variant="glass"
        hover="lift"
        className="p-6"
      >
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-white flex items-center">
            <ShoppingBag className="w-5 h-5 mr-2" />
            我的订单
          </h3>
          <div className="flex items-center space-x-3">
            <GlassButton
              variant="subtle"
              onClick={onRefreshOrders}
              disabled={isLoadingOrders}
              leftIcon={
                <RefreshCw className={`w-4 h-4 ${isLoadingOrders ? 'animate-spin' : ''}`} />
              }
            >
              刷新
            </GlassButton>
          </div>
        </div>

        <div className="space-y-4">
          {isLoadingOrders ? (
            <div className="text-center text-white/60 py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-2 border-white/30 border-t-white mx-auto mb-4"></div>
              <p>加载中...</p>
            </div>
          ) : orders.length === 0 ? (
            <div className="text-center text-white/60 py-8">
              <Package className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>您还没有绑定任何订单</p>
              <p className="text-sm mt-2 opacity-70">点击下方按钮开始绑定您的第一个订单</p>
            </div>
          ) : (
            orders.map((group) => (
              <OrderCard
                key={group.groupNumber}
                group={group}
                onViewDetails={() => onViewOrderDetails(group)}
              />
            ))
          )}
        </div>
      </GlassCard>

      {/* 操作按钮 */}
      <div className="space-y-4">
        <GlassButton
          variant="strong"
          size="lg"
          onClick={onBindNewOrder}
          className="w-full bg-gradient-to-r from-green-500/20 to-blue-600/20 hover:from-green-500/30 hover:to-blue-600/30"
          leftIcon={
            <div className="w-8 h-8 bg-white/20 rounded-xl flex items-center justify-center">
              <Plus className="w-5 h-5" />
            </div>
          }
        >
          <span className="text-lg">绑定新订单</span>
        </GlassButton>
      </div>
    </motion.div>
  )
}

// 订单卡片组件
interface OrderCardProps {
  group: OrderGroup
  onViewDetails: () => void
}

function OrderCard({ group, onViewDetails }: OrderCardProps) {
  const groupAvatarUrl = `https://p.qlogo.cn/gh/${group.groupNumber}/${group.groupNumber}/100`
  const skuTypesList = Array.from(group.skuTypes).join(' / ')
  
  // 计算状态
  let statusClass = 'status-pending'
  let statusText = '待激活'
  
  if (group.hasPermanent) {
    statusClass = 'status-active'
    statusText = '永久有效'
  } else if (group.totalDays > 0) {
    if (group.totalDays > 7) {
      statusClass = 'status-active'
      statusText = '正常'
    } else {
      statusClass = 'status-pending'
      statusText = '即将到期'
    }
  } else {
    statusClass = 'status-expired'
    statusText = '已过期'
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="glass rounded-2xl p-6 hover:-translate-y-2 hover:shadow-2xl transition-all duration-300"
    >
      <div className="flex items-start space-x-4">
        <div className="relative">
          <img
            src={groupAvatarUrl}
            alt="群头像"
            className="h-14 w-14 rounded-2xl object-cover shadow-lg border-2 border-white/20"
            onError={(e) => {
              e.currentTarget.src = 'https://placehold.co/56x56/667eea/ffffff?text=群'
            }}
          />
          <div className="absolute -bottom-1 -right-1 w-5 h-5 glass rounded-full flex items-center justify-center">
            <Users className="w-3 h-3 text-white" />
          </div>
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex justify-between items-start mb-3">
            <h4 className="font-semibold text-white text-lg truncate">
              群 {group.groupNumber}
            </h4>
            <span className="px-3 py-1 glass rounded-xl text-sm font-medium text-white whitespace-nowrap ml-2">
              {group.hasPermanent ? '永久' : group.totalDays > 0 ? `${group.totalDays}天` : '已过期'}
            </span>
          </div>

          <div className="space-y-2 mb-4">
            <div className="flex items-center text-white/80 text-sm">
              <Clock className="w-4 h-4 mr-2" />
              <span className={`status-indicator ${statusClass}`}>
                状态: {statusText}
              </span>
            </div>
            <div className="flex items-center text-white/80 text-sm">
              <Tag className="w-4 h-4 mr-2" />
              <span>档位: {skuTypesList}</span>
            </div>
          </div>

          <div className="flex justify-between items-center">
            <span className="text-white/60 text-sm flex items-center">
              <Package className="w-4 h-4 mr-1" />
              {group.items.length} 个订单
            </span>
            <GlassButton
              variant="subtle"
              size="sm"
              onClick={onViewDetails}
              rightIcon={<ChevronRight className="w-4 h-4" />}
            >
              查看详情
            </GlassButton>
          </div>
        </div>
      </div>
    </motion.div>
  )
}
