"use client"

import * as React from "react"
import { motion } from "framer-motion"
import {
  Database,
  Plus,
  Search,
  Filter,
  Download,
  ArrowDown,
  ArrowUp,
  Refresh<PERSON>w,
  Edit,
  Trash2
} from "lucide-react"
import { GlassCard, GlassButton, GlassInput } from "@/components/ui/glass"
import { EnhancedGlassCard } from "@/components/ui/enhanced-glass"
import { ModernTable, TableColumn, TableAction } from "@/components/ui/modern-table"
import { FadeIn, Stagger } from "@/components/ui/animated-elements"
import { BindingTable } from "./binding-table"
import { BindingModal } from "./binding-modal"
import { cn } from "@/lib/utils"
import type { Order, BindingData } from "@/types"

export interface AdminPanelProps {
  bindings: Order[]
  isLoading?: boolean
  onRefresh: () => void
  onAddBinding: (data: BindingData) => Promise<void>
  onEditBinding: (data: BindingData & { originalOrderNumber: string; originalSkuId: string }) => Promise<void>
  onDeleteBinding: (orderNumber: string, skuId: string) => Promise<void>
  onUpgradeBinding: (id: number) => Promise<void>
}

export function AdminPanel({
  bindings,
  isLoading = false,
  onRefresh,
  onAddBinding,
  onEditBinding,
  onDeleteBinding,
  onUpgradeBinding
}: AdminPanelProps) {
  const [searchQuery, setSearchQuery] = React.useState("")
  const [sortOrder, setSortOrder] = React.useState<'asc' | 'desc'>('desc')
  const [currentPage, setCurrentPage] = React.useState(1)
  const [itemsPerPage] = React.useState(10)
  const [showBindingModal, setShowBindingModal] = React.useState(false)
  const [editingBinding, setEditingBinding] = React.useState<Order | null>(null)
  const [modalLoading, setModalLoading] = React.useState(false)
  const [modalError, setModalError] = React.useState<string | null>(null)

  // 过滤和排序数据
  const filteredBindings = React.useMemo(() => {
    let filtered = bindings.filter(binding => 
      binding.orderNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
      binding.groupNumber.includes(searchQuery) ||
      (binding.owner && binding.owner.toLowerCase().includes(searchQuery.toLowerCase()))
    )

    // 排序
    filtered.sort((a, b) => {
      const dateA = new Date(a.bindTime || 0).getTime()
      const dateB = new Date(b.bindTime || 0).getTime()
      return sortOrder === 'desc' ? dateB - dateA : dateA - dateB
    })

    return filtered
  }, [bindings, searchQuery, sortOrder])

  // 分页数据
  const paginatedBindings = React.useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage
    return filteredBindings.slice(startIndex, startIndex + itemsPerPage)
  }, [filteredBindings, currentPage, itemsPerPage])

  const totalPages = Math.ceil(filteredBindings.length / itemsPerPage)

  // 处理添加绑定
  const handleAddBinding = async (data: BindingData) => {
    setModalLoading(true)
    setModalError(null)
    
    try {
      await onAddBinding(data)
      setShowBindingModal(false)
      setEditingBinding(null)
    } catch (error) {
      setModalError(error instanceof Error ? error.message : '操作失败')
    } finally {
      setModalLoading(false)
    }
  }

  // 处理编辑绑定
  const handleEditBinding = async (data: BindingData) => {
    if (!editingBinding) return
    
    setModalLoading(true)
    setModalError(null)
    
    try {
      await onEditBinding({
        ...data,
        originalOrderNumber: editingBinding.orderNumber,
        originalSkuId: editingBinding.skuId
      })
      setShowBindingModal(false)
      setEditingBinding(null)
    } catch (error) {
      setModalError(error instanceof Error ? error.message : '操作失败')
    } finally {
      setModalLoading(false)
    }
  }

  // 打开编辑模态框
  const handleEditClick = (binding: Order) => {
    setEditingBinding(binding)
    setShowBindingModal(true)
  }

  // 关闭模态框
  const handleCloseModal = () => {
    setShowBindingModal(false)
    setEditingBinding(null)
    setModalError(null)
  }

  // 切换排序
  const toggleSortOrder = () => {
    setSortOrder(prev => prev === 'desc' ? 'asc' : 'desc')
  }

  // 定义表格列
  const columns: TableColumn<Order>[] = [
    {
      key: "orderNumber",
      title: "订单号",
      sortable: true,
      width: "200px",
      render: (value) => (
        <span className="font-mono text-sm bg-white/10 px-2 py-1 rounded-lg">
          {value}
        </span>
      ),
    },
    {
      key: "groupNumber",
      title: "群组号",
      sortable: true,
      width: "150px",
      render: (value) => (
        <span className="text-cyan-300 font-medium">{value}</span>
      ),
    },
    {
      key: "skuType",
      title: "SKU类型",
      sortable: true,
      width: "120px",
      render: (value) => (
        <span className={cn(
          "px-2 py-1 rounded-full text-xs font-medium",
          value === "SKU_15" ? "bg-blue-500/20 text-blue-300" : "bg-purple-500/20 text-purple-300"
        )}>
          {value === "SKU_15" ? "15元/月" : "20元/月"}
        </span>
      ),
    },
    {
      key: "owner",
      title: "绑定用户",
      sortable: true,
      width: "150px",
      render: (value) => (
        <div className="flex items-center space-x-2">
          <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-xs font-bold text-white">
            {value?.charAt(0)?.toUpperCase() || "?"}
          </div>
          <span>{value || "未绑定"}</span>
        </div>
      ),
    },
    {
      key: "expirationDate",
      title: "到期时间",
      sortable: true,
      width: "180px",
      render: (value, record) => (
        <div className="space-y-1">
          <div className="text-sm">{value}</div>
          <div className={cn(
            "text-xs",
            record.remainingDays && record.remainingDays > 7 ? "text-green-400" :
            record.remainingDays && record.remainingDays > 3 ? "text-yellow-400" : "text-red-400"
          )}>
            {record.remainingDaysText}
          </div>
        </div>
      ),
    },
    {
      key: "isActive",
      title: "状态",
      sortable: true,
      width: "100px",
      align: "center",
      render: (value) => (
        <div className={cn(
          "w-2 h-2 rounded-full mx-auto",
          value ? "bg-green-400 animate-pulse" : "bg-red-400"
        )} />
      ),
    },
  ]

  // 定义表格操作
  const actions: TableAction<Order>[] = [
    {
      key: "edit",
      label: "编辑",
      icon: <Edit className="w-4 h-4" />,
      onClick: handleEditClick,
      variant: "default",
    },
    {
      key: "upgrade",
      label: "升级",
      icon: <ArrowUp className="w-4 h-4" />,
      onClick: (record) => onUpgradeBinding(record.id),
      variant: "primary",
      disabled: (record) => record.skuType === "SKU_20",
    },
    {
      key: "delete",
      label: "删除",
      icon: <Trash2 className="w-4 h-4" />,
      onClick: (record) => onDeleteBinding(record.orderNumber, record.skuId),
      variant: "danger",
    },
  ]

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="space-y-8"
    >
      {/* 顶部导航栏 */}
      <FadeIn delay={0.1}>
        <EnhancedGlassCard
          variant="cosmic"
          size="lg"
          glow="subtle"
          floating
          className="p-6"
        >
          <div className="flex flex-col lg:flex-row justify-between items-center gap-6">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-xl flex items-center justify-center backdrop-blur-sm">
                <Database className="w-6 h-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-semibold text-white bg-gradient-to-r from-white to-purple-200 bg-clip-text">
                  绑定管理
                </h2>
                <p className="text-white/70">管理所有订单绑定信息</p>
              </div>
          </div>

            <Stagger staggerDelay={0.1} className="flex flex-wrap gap-3">
              {/* 统计信息 */}
              <div className="flex items-center space-x-4 text-sm text-white/70">
                <span>总计: {bindings.length}</span>
                <span>活跃: {bindings.filter(b => b.isActive).length}</span>
                <span>即将到期: {bindings.filter(b => b.remainingDays && b.remainingDays <= 7).length}</span>
              </div>

              {/* 添加绑定按钮 */}
              <GlassButton
                variant="strong"
                onClick={() => setShowBindingModal(true)}
                className="bg-gradient-to-r from-green-500/20 to-emerald-600/20 hover:from-green-500/30 hover:to-emerald-600/30 border-green-400/30"
                leftIcon={<Plus className="w-5 h-5" />}
              >
                添加新绑定
              </GlassButton>
            </Stagger>
          </div>
        </EnhancedGlassCard>
      </FadeIn>

      {/* 现代化数据表格 */}
      <FadeIn delay={0.3}>
        <ModernTable
          columns={columns}
          data={filteredBindings}
          loading={isLoading}
          pagination={{
            current: currentPage,
            pageSize: itemsPerPage,
            total: filteredBindings.length,
            onChange: (page, pageSize) => setCurrentPage(page),
          }}
          actions={actions}
          searchable={true}
          onSearch={(value) => setSearchQuery(value)}
          onRefresh={onRefresh}
          rowKey="id"
          emptyText="暂无绑定数据"
        />
      </FadeIn>

      {/* 绑定模态框 */}
      <BindingModal
        isOpen={showBindingModal}
        onClose={handleCloseModal}
        onSubmit={editingBinding ? handleEditBinding : handleAddBinding}
        editingData={editingBinding}
        isLoading={modalLoading}
        error={modalError}
      />
    </motion.div>
  )
}
