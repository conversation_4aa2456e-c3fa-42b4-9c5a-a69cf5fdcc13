"use client"

import * as React from "react"
import { motion, useAnimation, useMotionValue, useTransform } from "framer-motion"
import { cn } from "@/lib/utils"

// 磁性按钮组件
export interface MagneticButtonProps {
  children: React.ReactNode
  className?: string
  strength?: number
  disabled?: boolean
  onClick?: () => void
}

export const MagneticButton: React.FC<MagneticButtonProps> = ({
  children,
  className,
  strength = 0.3,
  disabled = false,
  onClick,
}) => {
  const ref = React.useRef<HTMLButtonElement>(null)
  const x = useMotionValue(0)
  const y = useMotionValue(0)

  const handleMouseMove = (e: React.MouseEvent) => {
    if (disabled || !ref.current) return

    const rect = ref.current.getBoundingClientRect()
    const centerX = rect.left + rect.width / 2
    const centerY = rect.top + rect.height / 2
    
    const deltaX = (e.clientX - centerX) * strength
    const deltaY = (e.clientY - centerY) * strength
    
    x.set(deltaX)
    y.set(deltaY)
  }

  const handleMouseLeave = () => {
    x.set(0)
    y.set(0)
  }

  return (
    <motion.button
      ref={ref}
      style={{ x, y }}
      onMouseMove={handleMouseMove}
      onMouseLeave={handleMouseLeave}
      onClick={onClick}
      disabled={disabled}
      className={cn(
        "relative transition-all duration-300",
        disabled && "opacity-50 cursor-not-allowed",
        className
      )}
      whileHover={{ scale: disabled ? 1 : 1.05 }}
      whileTap={{ scale: disabled ? 1 : 0.95 }}
      transition={{ type: "spring", damping: 20, stiffness: 300 }}
    >
      {children}
    </motion.button>
  )
}

// 视差滚动组件
export interface ParallaxProps {
  children: React.ReactNode
  offset?: number
  className?: string
}

export const Parallax: React.FC<ParallaxProps> = ({
  children,
  offset = 50,
  className,
}) => {
  const [elementTop, setElementTop] = React.useState(0)
  const [clientHeight, setClientHeight] = React.useState(0)
  const ref = React.useRef<HTMLDivElement>(null)

  React.useEffect(() => {
    const element = ref.current
    if (!element) return

    const onScroll = () => {
      setElementTop(element.getBoundingClientRect().top)
      setClientHeight(window.innerHeight)
    }

    onScroll()
    window.addEventListener('scroll', onScroll)
    return () => window.removeEventListener('scroll', onScroll)
  }, [])

  const y = useTransform(
    useMotionValue(elementTop),
    [0, clientHeight],
    [0, -offset]
  )

  return (
    <motion.div
      ref={ref}
      style={{ y }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

// 鼠标跟随组件
export interface MouseFollowerProps {
  children: React.ReactNode
  className?: string
  speed?: number
  distance?: number
}

export const MouseFollower: React.FC<MouseFollowerProps> = ({
  children,
  className,
  speed = 0.1,
  distance = 20,
}) => {
  const ref = React.useRef<HTMLDivElement>(null)
  const x = useMotionValue(0)
  const y = useMotionValue(0)

  React.useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (!ref.current) return

      const rect = ref.current.getBoundingClientRect()
      const centerX = rect.left + rect.width / 2
      const centerY = rect.top + rect.height / 2
      
      const deltaX = (e.clientX - centerX) * speed
      const deltaY = (e.clientY - centerY) * speed
      
      x.set(Math.max(-distance, Math.min(distance, deltaX)))
      y.set(Math.max(-distance, Math.min(distance, deltaY)))
    }

    window.addEventListener('mousemove', handleMouseMove)
    return () => window.removeEventListener('mousemove', handleMouseMove)
  }, [speed, distance, x, y])

  return (
    <motion.div
      ref={ref}
      style={{ x, y }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

// 呼吸动画组件
export interface BreathingProps {
  children: React.ReactNode
  scale?: [number, number]
  duration?: number
  className?: string
}

export const Breathing: React.FC<BreathingProps> = ({
  children,
  scale = [1, 1.05],
  duration = 2,
  className,
}) => {
  return (
    <motion.div
      animate={{
        scale: scale,
      }}
      transition={{
        duration,
        repeat: Infinity,
        repeatType: "reverse",
        ease: "easeInOut",
      }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

// 打字机效果组件
export interface TypewriterProps {
  text: string
  speed?: number
  delay?: number
  className?: string
  onComplete?: () => void
  cursor?: boolean
}

export const Typewriter: React.FC<TypewriterProps> = ({
  text,
  speed = 50,
  delay = 0,
  className,
  onComplete,
  cursor = true,
}) => {
  const [displayText, setDisplayText] = React.useState("")
  const [currentIndex, setCurrentIndex] = React.useState(0)
  const [showCursor, setShowCursor] = React.useState(true)

  React.useEffect(() => {
    if (currentIndex < text.length) {
      const timeout = setTimeout(() => {
        setDisplayText(prev => prev + text[currentIndex])
        setCurrentIndex(prev => prev + 1)
      }, currentIndex === 0 ? delay : speed)

      return () => clearTimeout(timeout)
    } else {
      onComplete?.()
    }
  }, [currentIndex, text, speed, delay, onComplete])

  React.useEffect(() => {
    if (cursor) {
      const interval = setInterval(() => {
        setShowCursor(prev => !prev)
      }, 500)
      return () => clearInterval(interval)
    }
  }, [cursor])

  return (
    <span className={className}>
      {displayText}
      {cursor && (
        <motion.span
          animate={{ opacity: showCursor ? 1 : 0 }}
          className="inline-block w-0.5 h-5 bg-current ml-1"
        />
      )}
    </span>
  )
}

// 数字计数动画组件
export interface CountUpProps {
  from?: number
  to: number
  duration?: number
  delay?: number
  className?: string
  formatter?: (value: number) => string
}

export const CountUp: React.FC<CountUpProps> = ({
  from = 0,
  to,
  duration = 1,
  delay = 0,
  className,
  formatter = (value) => Math.round(value).toString(),
}) => {
  const [count, setCount] = React.useState(from)
  const controls = useAnimation()

  React.useEffect(() => {
    const timer = setTimeout(() => {
      controls.start({
        count: to,
        transition: {
          duration,
          ease: "easeOut",
          onUpdate: (latest) => {
            setCount(latest.count as number)
          },
        },
      })
    }, delay * 1000)

    return () => clearTimeout(timer)
  }, [to, duration, delay, controls])

  return (
    <motion.span
      animate={controls}
      className={className}
    >
      {formatter(count)}
    </motion.span>
  )
}

// 进度条动画组件
export interface AnimatedProgressProps {
  value: number
  max?: number
  className?: string
  barClassName?: string
  duration?: number
  showValue?: boolean
}

export const AnimatedProgress: React.FC<AnimatedProgressProps> = ({
  value,
  max = 100,
  className,
  barClassName,
  duration = 1,
  showValue = false,
}) => {
  const percentage = Math.min((value / max) * 100, 100)

  return (
    <div className={cn("relative w-full bg-white/10 rounded-full overflow-hidden", className)}>
      <motion.div
        initial={{ width: 0 }}
        animate={{ width: `${percentage}%` }}
        transition={{ duration, ease: "easeOut" }}
        className={cn(
          "h-full bg-gradient-to-r from-primary to-primary/80 rounded-full",
          barClassName
        )}
      />
      {showValue && (
        <div className="absolute inset-0 flex items-center justify-center text-xs font-medium text-white">
          <CountUp to={percentage} duration={duration} formatter={(v) => `${Math.round(v)}%`} />
        </div>
      )}
    </div>
  )
}

// 波纹效果组件
export interface RippleProps {
  children: React.ReactNode
  className?: string
  color?: string
  duration?: number
}

export const Ripple: React.FC<RippleProps> = ({
  children,
  className,
  color = "rgba(255, 255, 255, 0.3)",
  duration = 600,
}) => {
  const [ripples, setRipples] = React.useState<Array<{
    id: number
    x: number
    y: number
    size: number
  }>>([])

  const addRipple = (e: React.MouseEvent) => {
    const rect = e.currentTarget.getBoundingClientRect()
    const size = Math.max(rect.width, rect.height)
    const x = e.clientX - rect.left - size / 2
    const y = e.clientY - rect.top - size / 2
    
    const newRipple = {
      id: Date.now(),
      x,
      y,
      size,
    }

    setRipples(prev => [...prev, newRipple])

    setTimeout(() => {
      setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id))
    }, duration)
  }

  return (
    <div
      className={cn("relative overflow-hidden", className)}
      onMouseDown={addRipple}
    >
      {children}
      {ripples.map(ripple => (
        <motion.span
          key={ripple.id}
          initial={{
            scale: 0,
            opacity: 1,
          }}
          animate={{
            scale: 2,
            opacity: 0,
          }}
          transition={{
            duration: duration / 1000,
            ease: "easeOut",
          }}
          style={{
            position: "absolute",
            left: ripple.x,
            top: ripple.y,
            width: ripple.size,
            height: ripple.size,
            borderRadius: "50%",
            backgroundColor: color,
            pointerEvents: "none",
          }}
        />
      ))}
    </div>
  )
}

// 悬停发光效果组件
export interface GlowOnHoverProps {
  children: React.ReactNode
  className?: string
  glowColor?: string
  intensity?: number
}

export const GlowOnHover: React.FC<GlowOnHoverProps> = ({
  children,
  className,
  glowColor = "rgba(0, 122, 255, 0.5)",
  intensity = 20,
}) => {
  return (
    <motion.div
      className={cn("relative", className)}
      whileHover={{
        boxShadow: `0 0 ${intensity}px ${glowColor}`,
        transition: { duration: 0.3 },
      }}
      transition={{ duration: 0.3 }}
    >
      {children}
    </motion.div>
  )
}

export default {
  MagneticButton,
  Parallax,
  MouseFollower,
  Breathing,
  Typewriter,
  CountUp,
  AnimatedProgress,
  Ripple,
  GlowOnHover,
}
