"use client"

import * as React from "react"
import { motion, AnimatePresence } from "framer-motion"
import { Activity, Zap, Clock, Eye, EyeOff } from "lucide-react"
import { EnhancedGlassCard } from "./enhanced-glass"
import { cn } from "@/lib/utils"

interface PerformanceMetrics {
  fps: number
  memory: number
  loadTime: number
  renderTime: number
}

export interface PerformanceMonitorProps {
  className?: string
  showDetails?: boolean
}

export const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({
  className,
  showDetails = false,
}) => {
  const [metrics, setMetrics] = React.useState<PerformanceMetrics>({
    fps: 60,
    memory: 0,
    loadTime: 0,
    renderTime: 0,
  })
  const [isVisible, setIsVisible] = React.useState(showDetails)
  const [isMonitoring, setIsMonitoring] = React.useState(false)

  // FPS 监控
  React.useEffect(() => {
    if (!isMonitoring) return

    let frameCount = 0
    let lastTime = performance.now()
    let animationId: number

    const measureFPS = () => {
      frameCount++
      const currentTime = performance.now()
      
      if (currentTime >= lastTime + 1000) {
        const fps = Math.round((frameCount * 1000) / (currentTime - lastTime))
        setMetrics(prev => ({ ...prev, fps }))
        frameCount = 0
        lastTime = currentTime
      }
      
      animationId = requestAnimationFrame(measureFPS)
    }

    animationId = requestAnimationFrame(measureFPS)

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId)
      }
    }
  }, [isMonitoring])

  // 内存监控
  React.useEffect(() => {
    if (!isMonitoring) return

    const measureMemory = () => {
      if ('memory' in performance) {
        const memory = Math.round((performance as any).memory.usedJSHeapSize / 1024 / 1024)
        setMetrics(prev => ({ ...prev, memory }))
      }
    }

    const interval = setInterval(measureMemory, 1000)
    return () => clearInterval(interval)
  }, [isMonitoring])

  // 页面加载时间
  React.useEffect(() => {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    if (navigation) {
      const loadTime = Math.round(navigation.loadEventEnd - navigation.fetchStart)
      const renderTime = Math.round(navigation.domContentLoadedEventEnd - navigation.fetchStart)
      setMetrics(prev => ({ ...prev, loadTime, renderTime }))
    }
  }, [])

  const toggleMonitoring = () => {
    setIsMonitoring(!isMonitoring)
    if (!isMonitoring) {
      setIsVisible(true)
    }
  }

  const getPerformanceColor = (value: number, type: 'fps' | 'memory' | 'time') => {
    switch (type) {
      case 'fps':
        if (value >= 55) return 'text-green-400'
        if (value >= 30) return 'text-yellow-400'
        return 'text-red-400'
      case 'memory':
        if (value <= 50) return 'text-green-400'
        if (value <= 100) return 'text-yellow-400'
        return 'text-red-400'
      case 'time':
        if (value <= 1000) return 'text-green-400'
        if (value <= 3000) return 'text-yellow-400'
        return 'text-red-400'
      default:
        return 'text-white'
    }
  }

  return (
    <>
      {/* 切换按钮 */}
      <motion.button
        onClick={toggleMonitoring}
        className={cn(
          "fixed bottom-4 right-4 z-50 w-12 h-12 rounded-full",
          "bg-gradient-to-br from-purple-500/20 to-pink-500/20",
          "backdrop-blur-[15px] border border-white/20",
          "flex items-center justify-center",
          "hover:scale-110 transition-all duration-300",
          "focus:outline-none focus:ring-2 focus:ring-purple-400/50",
          className
        )}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
      >
        {isMonitoring ? (
          <Eye className="w-5 h-5 text-white" />
        ) : (
          <Activity className="w-5 h-5 text-white" />
        )}
      </motion.button>

      {/* 性能监控面板 */}
      <AnimatePresence>
        {isVisible && isMonitoring && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ duration: 0.3 }}
            className="fixed bottom-20 right-4 z-40"
          >
            <EnhancedGlassCard
              variant="dark"
              size="sm"
              glow="subtle"
              className="w-64"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <Activity className="w-4 h-4 text-purple-400" />
                  <span className="text-sm font-medium text-white">性能监控</span>
                </div>
                <button
                  onClick={() => setIsVisible(false)}
                  className="text-white/60 hover:text-white transition-colors"
                >
                  <EyeOff className="w-4 h-4" />
                </button>
              </div>

              <div className="space-y-3">
                {/* FPS */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Zap className="w-3 h-3 text-yellow-400" />
                    <span className="text-xs text-white/80">FPS</span>
                  </div>
                  <span className={cn("text-sm font-mono", getPerformanceColor(metrics.fps, 'fps'))}>
                    {metrics.fps}
                  </span>
                </div>

                {/* 内存使用 */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Activity className="w-3 h-3 text-blue-400" />
                    <span className="text-xs text-white/80">内存</span>
                  </div>
                  <span className={cn("text-sm font-mono", getPerformanceColor(metrics.memory, 'memory'))}>
                    {metrics.memory}MB
                  </span>
                </div>

                {/* 加载时间 */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Clock className="w-3 h-3 text-green-400" />
                    <span className="text-xs text-white/80">加载</span>
                  </div>
                  <span className={cn("text-sm font-mono", getPerformanceColor(metrics.loadTime, 'time'))}>
                    {metrics.loadTime}ms
                  </span>
                </div>

                {/* 渲染时间 */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Clock className="w-3 h-3 text-purple-400" />
                    <span className="text-xs text-white/80">渲染</span>
                  </div>
                  <span className={cn("text-sm font-mono", getPerformanceColor(metrics.renderTime, 'time'))}>
                    {metrics.renderTime}ms
                  </span>
                </div>
              </div>

              {/* 性能指示器 */}
              <div className="mt-4 pt-3 border-t border-white/10">
                <div className="flex items-center space-x-2">
                  <div className="flex space-x-1">
                    <div className={cn(
                      "w-2 h-2 rounded-full",
                      metrics.fps >= 55 ? "bg-green-400" : metrics.fps >= 30 ? "bg-yellow-400" : "bg-red-400"
                    )} />
                    <div className={cn(
                      "w-2 h-2 rounded-full",
                      metrics.memory <= 50 ? "bg-green-400" : metrics.memory <= 100 ? "bg-yellow-400" : "bg-red-400"
                    )} />
                    <div className={cn(
                      "w-2 h-2 rounded-full",
                      metrics.loadTime <= 1000 ? "bg-green-400" : metrics.loadTime <= 3000 ? "bg-yellow-400" : "bg-red-400"
                    )} />
                  </div>
                  <span className="text-xs text-white/60">
                    {metrics.fps >= 55 && metrics.memory <= 50 && metrics.loadTime <= 1000 
                      ? "优秀" 
                      : metrics.fps >= 30 && metrics.memory <= 100 && metrics.loadTime <= 3000 
                      ? "良好" 
                      : "需优化"
                    }
                  </span>
                </div>
              </div>
            </EnhancedGlassCard>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}

export default PerformanceMonitor
