"use client"

import * as React from "react"
import { motion, AnimatePresence } from "framer-motion"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

// 增强的玻璃拟态卡片组件
const enhancedGlassVariants = cva(
  "relative overflow-hidden backdrop-blur-[25px] border transition-all duration-500",
  {
    variants: {
      variant: {
        aurora: "bg-gradient-to-br from-purple-500/10 via-pink-500/10 to-blue-500/10 border-white/20",
        cosmic: "bg-gradient-to-br from-indigo-500/10 via-purple-500/10 to-pink-500/10 border-purple-400/30",
        ocean: "bg-gradient-to-br from-blue-500/10 via-cyan-500/10 to-teal-500/10 border-cyan-400/20",
        sunset: "bg-gradient-to-br from-orange-500/10 via-red-500/10 to-pink-500/10 border-orange-400/20",
        forest: "bg-gradient-to-br from-green-500/10 via-emerald-500/10 to-teal-500/10 border-green-400/20",
        minimal: "bg-white/8 border-white/15",
        dark: "bg-black/20 border-white/10",
      },
      size: {
        sm: "p-4",
        default: "p-6",
        lg: "p-8",
        xl: "p-12",
      },
      rounded: {
        default: "rounded-2xl",
        lg: "rounded-3xl",
        xl: "rounded-[2rem]",
      },
      glow: {
        none: "",
        subtle: "shadow-lg shadow-current/10",
        strong: "shadow-2xl shadow-current/20",
        intense: "shadow-[0_0_50px_rgba(var(--primary),0.3)]",
      },
    },
    defaultVariants: {
      variant: "minimal",
      size: "default",
      rounded: "default",
      glow: "none",
    },
  }
)

export interface EnhancedGlassCardProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof enhancedGlassVariants> {
  animated?: boolean
  shimmer?: boolean
  floating?: boolean
  children: React.ReactNode
}

export const EnhancedGlassCard = React.forwardRef<HTMLDivElement, EnhancedGlassCardProps>(
  ({ 
    className, 
    variant, 
    size, 
    rounded, 
    glow, 
    animated = true, 
    shimmer = false, 
    floating = false,
    children, 
    ...props 
  }, ref) => {
    const [isHovered, setIsHovered] = React.useState(false)

    const cardContent = (
      <div
        ref={ref}
        className={cn(
          enhancedGlassVariants({ variant, size, rounded, glow }),
          floating && "hover:-translate-y-2 hover:shadow-2xl",
          shimmer && "before:absolute before:inset-0 before:bg-gradient-to-r before:from-transparent before:via-white/10 before:to-transparent before:translate-x-[-100%] hover:before:translate-x-[100%] before:transition-transform before:duration-1000",
          className
        )}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        {...props}
      >
        {/* 动态背景效果 */}
        {variant === "aurora" && (
          <div className="absolute inset-0 opacity-30">
            <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-br from-purple-400/20 via-pink-400/20 to-blue-400/20 animate-pulse" />
            <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-purple-500/20 rounded-full blur-xl animate-float" />
            <div className="absolute bottom-1/4 right-1/4 w-24 h-24 bg-pink-500/20 rounded-full blur-xl animate-float" style={{ animationDelay: "2s" }} />
          </div>
        )}

        {/* 内容 */}
        <div className="relative z-10">
          {children}
        </div>

        {/* 边框光效 */}
        {isHovered && glow !== "none" && (
          <div className="absolute inset-0 rounded-inherit bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-50" />
        )}
      </div>
    )

    if (animated) {
      return (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, ease: "easeOut" }}
        >
          {cardContent}
        </motion.div>
      )
    }

    return cardContent
  }
)
EnhancedGlassCard.displayName = "EnhancedGlassCard"

// 玻璃拟态导航栏组件
export interface GlassNavbarProps extends React.HTMLAttributes<HTMLElement> {
  blur?: "light" | "medium" | "strong"
  sticky?: boolean
  children: React.ReactNode
}

export const GlassNavbar = React.forwardRef<HTMLElement, GlassNavbarProps>(
  ({ className, blur = "medium", sticky = true, children, ...props }, ref) => {
    const blurClasses = {
      light: "backdrop-blur-[15px]",
      medium: "backdrop-blur-[20px]",
      strong: "backdrop-blur-[30px]",
    }

    return (
      <nav
        ref={ref}
        className={cn(
          "w-full bg-white/10 border-b border-white/20 transition-all duration-300",
          blurClasses[blur],
          sticky && "sticky top-0 z-50",
          className
        )}
        {...props}
      >
        {children}
      </nav>
    )
  }
)
GlassNavbar.displayName = "GlassNavbar"

// 玻璃拟态侧边栏组件
export interface GlassSidebarProps extends React.HTMLAttributes<HTMLDivElement> {
  isOpen: boolean
  onClose: () => void
  side?: "left" | "right"
  children: React.ReactNode
}

export const GlassSidebar = React.forwardRef<HTMLDivElement, GlassSidebarProps>(
  ({ className, isOpen, onClose, side = "left", children, ...props }, ref) => {
    return (
      <AnimatePresence>
        {isOpen && (
          <>
            {/* 背景遮罩 */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
              onClick={onClose}
            />

            {/* 侧边栏 */}
            <motion.div
              ref={ref}
              initial={{ x: side === "left" ? "-100%" : "100%" }}
              animate={{ x: 0 }}
              exit={{ x: side === "left" ? "-100%" : "100%" }}
              transition={{ type: "spring", damping: 25, stiffness: 300 }}
              className={cn(
                "fixed top-0 h-full w-80 bg-white/10 backdrop-blur-[25px] border-r border-white/20 z-50",
                side === "left" ? "left-0" : "right-0",
                className
              )}
              {...props}
            >
              {children}
            </motion.div>
          </>
        )}
      </AnimatePresence>
    )
  }
)
GlassSidebar.displayName = "GlassSidebar"

// 玻璃拟态工具提示组件
export interface GlassTooltipProps {
  content: React.ReactNode
  children: React.ReactNode
  side?: "top" | "bottom" | "left" | "right"
  delay?: number
}

export const GlassTooltip: React.FC<GlassTooltipProps> = ({
  content,
  children,
  side = "top",
  delay = 500,
}) => {
  const [isVisible, setIsVisible] = React.useState(false)
  const timeoutRef = React.useRef<NodeJS.Timeout>()

  const showTooltip = () => {
    timeoutRef.current = setTimeout(() => setIsVisible(true), delay)
  }

  const hideTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    setIsVisible(false)
  }

  const positionClasses = {
    top: "bottom-full left-1/2 transform -translate-x-1/2 mb-2",
    bottom: "top-full left-1/2 transform -translate-x-1/2 mt-2",
    left: "right-full top-1/2 transform -translate-y-1/2 mr-2",
    right: "left-full top-1/2 transform -translate-y-1/2 ml-2",
  }

  return (
    <div
      className="relative inline-block"
      onMouseEnter={showTooltip}
      onMouseLeave={hideTooltip}
    >
      {children}
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            transition={{ duration: 0.2 }}
            className={cn(
              "absolute z-50 px-3 py-2 text-sm text-white bg-black/80 backdrop-blur-[15px] border border-white/20 rounded-lg shadow-lg",
              positionClasses[side]
            )}
          >
            {content}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
GlassTooltip.displayName = "GlassTooltip"
