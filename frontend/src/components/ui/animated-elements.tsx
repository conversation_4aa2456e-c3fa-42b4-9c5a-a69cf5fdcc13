"use client"

import * as React from "react"
import { motion, AnimatePresence, useInView, useAnimation } from "framer-motion"
import { cn } from "@/lib/utils"

// 淡入动画组件
export interface FadeInProps {
  children: React.ReactNode
  delay?: number
  duration?: number
  direction?: "up" | "down" | "left" | "right"
  className?: string
}

export const FadeIn: React.FC<FadeInProps> = ({
  children,
  delay = 0,
  duration = 0.6,
  direction = "up",
  className,
}) => {
  const ref = React.useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  const variants = {
    hidden: {
      opacity: 0,
      y: direction === "up" ? 30 : direction === "down" ? -30 : 0,
      x: direction === "left" ? 30 : direction === "right" ? -30 : 0,
    },
    visible: {
      opacity: 1,
      y: 0,
      x: 0,
    },
  }

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={variants}
      transition={{
        duration,
        delay,
        ease: [0.25, 0.25, 0, 1],
      }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

// 交错动画组件
export interface StaggerProps {
  children: React.ReactNode
  staggerDelay?: number
  className?: string
}

export const Stagger: React.FC<StaggerProps> = ({
  children,
  staggerDelay = 0.1,
  className,
}) => {
  const ref = React.useRef(null)
  const isInView = useInView(ref, { once: true })

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: staggerDelay,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: [0.25, 0.25, 0, 1],
      },
    },
  }

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={isInView ? "visible" : "hidden"}
      variants={containerVariants}
      className={className}
    >
      {React.Children.map(children, (child, index) => (
        <motion.div key={index} variants={itemVariants}>
          {child}
        </motion.div>
      ))}
    </motion.div>
  )
}

// 悬浮动画组件
export interface FloatingProps {
  children: React.ReactNode
  intensity?: "subtle" | "medium" | "strong"
  speed?: "slow" | "medium" | "fast"
  className?: string
}

export const Floating: React.FC<FloatingProps> = ({
  children,
  intensity = "medium",
  speed = "medium",
  className,
}) => {
  const intensityValues = {
    subtle: 5,
    medium: 10,
    strong: 15,
  }

  const speedValues = {
    slow: 6,
    medium: 4,
    fast: 2,
  }

  return (
    <motion.div
      animate={{
        y: [-intensityValues[intensity], intensityValues[intensity]],
        rotate: [-1, 1],
      }}
      transition={{
        duration: speedValues[speed],
        repeat: Infinity,
        repeatType: "reverse",
        ease: "easeInOut",
      }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

// 脉冲动画组件
export interface PulseProps {
  children: React.ReactNode
  scale?: number
  duration?: number
  className?: string
}

export const Pulse: React.FC<PulseProps> = ({
  children,
  scale = 1.05,
  duration = 2,
  className,
}) => {
  return (
    <motion.div
      animate={{
        scale: [1, scale, 1],
      }}
      transition={{
        duration,
        repeat: Infinity,
        ease: "easeInOut",
      }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

// 旋转动画组件
export interface SpinProps {
  children: React.ReactNode
  direction?: "clockwise" | "counterclockwise"
  speed?: "slow" | "medium" | "fast"
  className?: string
}

export const Spin: React.FC<SpinProps> = ({
  children,
  direction = "clockwise",
  speed = "medium",
  className,
}) => {
  const speedValues = {
    slow: 4,
    medium: 2,
    fast: 1,
  }

  const rotation = direction === "clockwise" ? 360 : -360

  return (
    <motion.div
      animate={{ rotate: rotation }}
      transition={{
        duration: speedValues[speed],
        repeat: Infinity,
        ease: "linear",
      }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

// 弹跳动画组件
export interface BounceProps {
  children: React.ReactNode
  height?: number
  duration?: number
  className?: string
}

export const Bounce: React.FC<BounceProps> = ({
  children,
  height = 10,
  duration = 0.6,
  className,
}) => {
  return (
    <motion.div
      animate={{
        y: [0, -height, 0],
      }}
      transition={{
        duration,
        repeat: Infinity,
        ease: "easeOut",
      }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

// 摇摆动画组件
export interface WiggleProps {
  children: React.ReactNode
  intensity?: number
  duration?: number
  className?: string
}

export const Wiggle: React.FC<WiggleProps> = ({
  children,
  intensity = 3,
  duration = 0.5,
  className,
}) => {
  return (
    <motion.div
      animate={{
        rotate: [-intensity, intensity, -intensity, intensity, 0],
      }}
      transition={{
        duration,
        repeat: Infinity,
        repeatDelay: 2,
      }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

// 打字机效果组件
export interface TypewriterProps {
  text: string
  speed?: number
  delay?: number
  className?: string
  onComplete?: () => void
}

export const Typewriter: React.FC<TypewriterProps> = ({
  text,
  speed = 50,
  delay = 0,
  className,
  onComplete,
}) => {
  const [displayText, setDisplayText] = React.useState("")
  const [currentIndex, setCurrentIndex] = React.useState(0)

  React.useEffect(() => {
    if (currentIndex < text.length) {
      const timeout = setTimeout(() => {
        setDisplayText(prev => prev + text[currentIndex])
        setCurrentIndex(prev => prev + 1)
      }, currentIndex === 0 ? delay : speed)

      return () => clearTimeout(timeout)
    } else if (onComplete) {
      onComplete()
    }
  }, [currentIndex, text, speed, delay, onComplete])

  return (
    <span className={className}>
      {displayText}
      <motion.span
        animate={{ opacity: [1, 0] }}
        transition={{ duration: 0.8, repeat: Infinity }}
        className="inline-block w-0.5 h-5 bg-current ml-1"
      />
    </span>
  )
}

// 页面转场动画组件
export interface PageTransitionProps {
  children: React.ReactNode
  className?: string
}

export const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  className,
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{
        duration: 0.3,
        ease: [0.25, 0.25, 0, 1],
      }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

// 滚动触发动画组件
export interface ScrollRevealProps {
  children: React.ReactNode
  threshold?: number
  rootMargin?: string
  className?: string
}

export const ScrollReveal: React.FC<ScrollRevealProps> = ({
  children,
  threshold = 0.1,
  rootMargin = "0px",
  className,
}) => {
  const ref = React.useRef(null)
  const isInView = useInView(ref, { 
    once: true, 
    amount: threshold,
    margin: rootMargin 
  })
  const controls = useAnimation()

  React.useEffect(() => {
    if (isInView) {
      controls.start("visible")
    }
  }, [isInView, controls])

  return (
    <motion.div
      ref={ref}
      initial="hidden"
      animate={controls}
      variants={{
        hidden: { opacity: 0, y: 50 },
        visible: { 
          opacity: 1, 
          y: 0,
          transition: {
            duration: 0.6,
            ease: [0.25, 0.25, 0, 1],
          }
        },
      }}
      className={className}
    >
      {children}
    </motion.div>
  )
}
