"use client"

import * as React from "react"
import { motion, AnimatePresence } from "framer-motion"
import { 
  ChevronUp, 
  ChevronDown, 
  Search, 
  Filter, 
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  Download,
  RefreshCw
} from "lucide-react"
import { EnhancedGlassCard } from "./enhanced-glass"
import { GlassButton } from "./glass"
import { FadeIn, Stagger } from "./animated-elements"
import { cn } from "@/lib/utils"

export interface TableColumn<T = any> {
  key: string
  title: string
  width?: string
  sortable?: boolean
  render?: (value: any, record: T, index: number) => React.ReactNode
  align?: "left" | "center" | "right"
}

export interface TableAction<T = any> {
  key: string
  label: string
  icon?: React.ReactNode
  onClick: (record: T) => void
  variant?: "default" | "primary" | "danger"
  disabled?: (record: T) => boolean
}

export interface ModernTableProps<T = any> {
  columns: TableColumn<T>[]
  data: T[]
  loading?: boolean
  pagination?: {
    current: number
    pageSize: number
    total: number
    onChange: (page: number, pageSize: number) => void
  }
  actions?: TableAction<T>[]
  searchable?: boolean
  filterable?: boolean
  onSearch?: (value: string) => void
  onFilter?: (filters: Record<string, any>) => void
  onRefresh?: () => void
  className?: string
  rowKey?: string | ((record: T) => string)
  emptyText?: string
}

export function ModernTable<T = any>({
  columns,
  data,
  loading = false,
  pagination,
  actions,
  searchable = true,
  filterable = false,
  onSearch,
  onFilter,
  onRefresh,
  className,
  rowKey = "id",
  emptyText = "暂无数据",
}: ModernTableProps<T>) {
  const [sortConfig, setSortConfig] = React.useState<{
    key: string
    direction: "asc" | "desc"
  } | null>(null)
  const [searchValue, setSearchValue] = React.useState("")
  const [selectedRows, setSelectedRows] = React.useState<Set<string>>(new Set())

  const getRowKey = (record: T, index: number): string => {
    if (typeof rowKey === "function") {
      return rowKey(record)
    }
    return (record as any)[rowKey] || index.toString()
  }

  const handleSort = (key: string) => {
    let direction: "asc" | "desc" = "asc"
    if (sortConfig && sortConfig.key === key && sortConfig.direction === "asc") {
      direction = "desc"
    }
    setSortConfig({ key, direction })
  }

  const handleSearch = (value: string) => {
    setSearchValue(value)
    onSearch?.(value)
  }

  const sortedData = React.useMemo(() => {
    if (!sortConfig) return data

    return [...data].sort((a, b) => {
      const aValue = (a as any)[sortConfig.key]
      const bValue = (b as any)[sortConfig.key]

      if (aValue < bValue) {
        return sortConfig.direction === "asc" ? -1 : 1
      }
      if (aValue > bValue) {
        return sortConfig.direction === "asc" ? 1 : -1
      }
      return 0
    })
  }, [data, sortConfig])

  const renderSortIcon = (columnKey: string) => {
    if (!sortConfig || sortConfig.key !== columnKey) {
      return <ChevronUp className="w-4 h-4 text-white/30" />
    }
    return sortConfig.direction === "asc" ? (
      <ChevronUp className="w-4 h-4 text-primary" />
    ) : (
      <ChevronDown className="w-4 h-4 text-primary" />
    )
  }

  const renderActions = (record: T) => {
    if (!actions || actions.length === 0) return null

    return (
      <div className="flex items-center space-x-2">
        {actions.slice(0, 2).map((action) => (
          <GlassButton
            key={action.key}
            size="sm"
            variant={action.variant === "danger" ? "destructive" : "subtle"}
            onClick={() => action.onClick(record)}
            disabled={action.disabled?.(record)}
            className="p-2"
          >
            {action.icon}
          </GlassButton>
        ))}
        {actions.length > 2 && (
          <GlassButton
            size="sm"
            variant="subtle"
            className="p-2"
          >
            <MoreHorizontal className="w-4 h-4" />
          </GlassButton>
        )}
      </div>
    )
  }

  return (
    <EnhancedGlassCard
      variant="minimal"
      size="lg"
      className={cn("w-full", className)}
    >
      {/* 表格头部工具栏 */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
        <div className="flex items-center space-x-4">
          {searchable && (
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-white/50" />
              <input
                type="text"
                placeholder="搜索..."
                value={searchValue}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all duration-300"
              />
            </div>
          )}
          {filterable && (
            <GlassButton
              variant="subtle"
              size="sm"
              leftIcon={<Filter className="w-4 h-4" />}
            >
              筛选
            </GlassButton>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {onRefresh && (
            <GlassButton
              variant="subtle"
              size="sm"
              onClick={onRefresh}
              leftIcon={<RefreshCw className="w-4 h-4" />}
            >
              刷新
            </GlassButton>
          )}
          <GlassButton
            variant="subtle"
            size="sm"
            leftIcon={<Download className="w-4 h-4" />}
          >
            导出
          </GlassButton>
        </div>
      </div>

      {/* 表格容器 */}
      <div className="overflow-x-auto">
        <table className="w-full">
          {/* 表头 */}
          <thead>
            <tr className="border-b border-white/10">
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={cn(
                    "px-4 py-4 text-left text-sm font-medium text-white/90",
                    column.align === "center" && "text-center",
                    column.align === "right" && "text-right",
                    column.sortable && "cursor-pointer hover:text-white transition-colors",
                    column.width && `w-[${column.width}]`
                  )}
                  onClick={() => column.sortable && handleSort(column.key)}
                >
                  <div className="flex items-center space-x-2">
                    <span>{column.title}</span>
                    {column.sortable && renderSortIcon(column.key)}
                  </div>
                </th>
              ))}
              {actions && actions.length > 0 && (
                <th className="px-4 py-4 text-right text-sm font-medium text-white/90 w-32">
                  操作
                </th>
              )}
            </tr>
          </thead>

          {/* 表体 */}
          <tbody>
            <AnimatePresence>
              {loading ? (
                <tr>
                  <td colSpan={columns.length + (actions ? 1 : 0)} className="px-4 py-8 text-center">
                    <div className="flex items-center justify-center space-x-2">
                      <RefreshCw className="w-5 h-5 animate-spin text-primary" />
                      <span className="text-white/70">加载中...</span>
                    </div>
                  </td>
                </tr>
              ) : sortedData.length === 0 ? (
                <tr>
                  <td colSpan={columns.length + (actions ? 1 : 0)} className="px-4 py-8 text-center text-white/70">
                    {emptyText}
                  </td>
                </tr>
              ) : (
                <Stagger staggerDelay={0.05}>
                  {sortedData.map((record, index) => (
                    <motion.tr
                      key={getRowKey(record, index)}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      className="border-b border-white/5 hover:bg-white/5 transition-colors duration-200"
                    >
                      {columns.map((column) => (
                        <td
                          key={column.key}
                          className={cn(
                            "px-4 py-4 text-sm text-white/80",
                            column.align === "center" && "text-center",
                            column.align === "right" && "text-right"
                          )}
                        >
                          {column.render
                            ? column.render((record as any)[column.key], record, index)
                            : (record as any)[column.key]
                          }
                        </td>
                      ))}
                      {actions && actions.length > 0 && (
                        <td className="px-4 py-4 text-right">
                          {renderActions(record)}
                        </td>
                      )}
                    </motion.tr>
                  ))}
                </Stagger>
              )}
            </AnimatePresence>
          </tbody>
        </table>
      </div>

      {/* 分页 */}
      {pagination && (
        <div className="flex items-center justify-between mt-6 pt-4 border-t border-white/10">
          <div className="text-sm text-white/70">
            共 {pagination.total} 条记录
          </div>
          <div className="flex items-center space-x-2">
            <GlassButton
              variant="subtle"
              size="sm"
              disabled={pagination.current <= 1}
              onClick={() => pagination.onChange(pagination.current - 1, pagination.pageSize)}
            >
              上一页
            </GlassButton>
            <span className="px-3 py-1 text-sm text-white/80">
              {pagination.current} / {Math.ceil(pagination.total / pagination.pageSize)}
            </span>
            <GlassButton
              variant="subtle"
              size="sm"
              disabled={pagination.current >= Math.ceil(pagination.total / pagination.pageSize)}
              onClick={() => pagination.onChange(pagination.current + 1, pagination.pageSize)}
            >
              下一页
            </GlassButton>
          </div>
        </div>
      )}
    </EnhancedGlassCard>
  )
}
