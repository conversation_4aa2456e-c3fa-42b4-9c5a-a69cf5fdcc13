"use client"

import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"

const glassVariants = cva(
  "backdrop-blur-[20px] border transition-all duration-300 relative overflow-hidden",
  {
    variants: {
      variant: {
        default: "bg-white/10 border-white/20",
        strong: "bg-white/15 border-white/25 backdrop-blur-[25px]",
        dark: "bg-black/20 border-white/10",
        subtle: "bg-white/5 border-white/10 backdrop-blur-[15px]",
        colored: "bg-gradient-to-br from-white/10 to-white/5 border-white/20",
        gradient: "bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-pink-500/10 border-white/20",
        frosted: "bg-white/8 border-white/15 backdrop-blur-[30px] backdrop-saturate-150",
        neon: "bg-gradient-to-br from-cyan-500/10 to-blue-500/10 border-cyan-400/30 shadow-cyan-500/20",
        warm: "bg-gradient-to-br from-orange-500/10 to-red-500/10 border-orange-400/20",
        cool: "bg-gradient-to-br from-blue-500/10 to-indigo-500/10 border-blue-400/20",
      },
      rounded: {
        none: "rounded-none",
        sm: "rounded-lg",
        default: "rounded-2xl",
        lg: "rounded-3xl",
        xl: "rounded-[2rem]",
        full: "rounded-full",
      },
      shadow: {
        none: "shadow-none",
        sm: "shadow-lg shadow-black/5",
        default: "shadow-xl shadow-black/10",
        lg: "shadow-2xl shadow-black/15",
        xl: "shadow-[0_35px_60px_-15px_rgba(0,0,0,0.3)]",
        glow: "shadow-2xl shadow-primary/20",
      },
      hover: {
        none: "",
        lift: "hover:-translate-y-2 hover:shadow-2xl transition-transform duration-300 ease-out",
        scale: "hover:scale-[1.02] transition-transform duration-300 ease-spring",
        glow: "hover:shadow-2xl hover:shadow-primary/20 transition-shadow duration-300",
        all: "hover:-translate-y-2 hover:scale-[1.02] hover:shadow-2xl transition-all duration-300 ease-out",
        float: "hover:-translate-y-1 hover:shadow-xl transition-all duration-500 ease-out",
        shimmer: "hover:animate-shimmer",
      },
      border: {
        none: "border-0",
        thin: "border",
        thick: "border-2",
        gradient: "border border-transparent bg-gradient-to-r from-white/20 via-white/10 to-white/20 bg-clip-padding",
      },
    },
    defaultVariants: {
      variant: "default",
      rounded: "default",
      shadow: "default",
      hover: "none",
      border: "thin",
    },
  }
)

export interface GlassProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof glassVariants> {
  asChild?: boolean
  shimmer?: boolean
  glow?: boolean
}

const Glass = React.forwardRef<HTMLDivElement, GlassProps>(
  ({ className, variant, rounded, shadow, hover, border, shimmer, glow, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          glassVariants({ variant, rounded, shadow, hover, border }),
          shimmer && "animate-shimmer",
          glow && "animate-glow",
          className
        )}
        {...props}
      />
    )
  }
)
Glass.displayName = "Glass"

// 玻璃拟态容器组件
export interface GlassContainerProps extends GlassProps {
  children: React.ReactNode
  padding?: "none" | "sm" | "default" | "lg" | "xl"
}

const paddingClasses = {
  none: "p-0",
  sm: "p-4",
  default: "p-6",
  lg: "p-8",
  xl: "p-12",
}

const GlassContainer = React.forwardRef<HTMLDivElement, GlassContainerProps>(
  ({ className, padding = "default", children, ...props }, ref) => {
    return (
      <Glass
        ref={ref}
        className={cn(paddingClasses[padding], className)}
        {...props}
      >
        {children}
      </Glass>
    )
  }
)
GlassContainer.displayName = "GlassContainer"

// 玻璃拟态卡片组件
export interface GlassCardProps extends GlassContainerProps {
  title?: string
  description?: string
  icon?: React.ReactNode
  headerActions?: React.ReactNode
}

const GlassCard = React.forwardRef<HTMLDivElement, GlassCardProps>(
  ({ 
    className, 
    title, 
    description, 
    icon, 
    headerActions,
    children, 
    ...props 
  }, ref) => {
    return (
      <GlassContainer
        ref={ref}
        className={cn("space-y-6", className)}
        {...props}
      >
        {(title || description || icon || headerActions) && (
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-3">
              {icon && (
                <div className="w-10 h-10 glass rounded-xl flex items-center justify-center">
                  {icon}
                </div>
              )}
              <div>
                {title && (
                  <h3 className="text-xl font-semibold text-white">
                    {title}
                  </h3>
                )}
                {description && (
                  <p className="text-white/70 mt-1">
                    {description}
                  </p>
                )}
              </div>
            </div>
            {headerActions && (
              <div className="flex items-center space-x-2">
                {headerActions}
              </div>
            )}
          </div>
        )}
        {children}
      </GlassContainer>
    )
  }
)
GlassCard.displayName = "GlassCard"

// 玻璃拟态按钮组件
export interface GlassButtonProps 
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: "default" | "strong" | "subtle"
  size?: "sm" | "default" | "lg" | "icon"
  loading?: boolean
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
}

const glassButtonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-2xl font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-white/30 disabled:pointer-events-none disabled:opacity-50 text-white backdrop-blur-[20px] border",
  {
    variants: {
      variant: {
        default: "bg-white/10 border-white/20 hover:bg-white/20 hover:-translate-y-0.5 active:scale-95",
        strong: "bg-white/15 border-white/25 hover:bg-white/25 hover:-translate-y-0.5 active:scale-95 backdrop-blur-[25px]",
        subtle: "bg-white/5 border-white/10 hover:bg-white/15 hover:-translate-y-0.5 active:scale-95 backdrop-blur-[15px]",
      },
      size: {
        sm: "h-10 px-4 py-2 text-sm",
        default: "h-12 px-6 py-3 text-sm",
        lg: "h-14 px-8 py-4 text-base",
        icon: "h-12 w-12",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

const GlassButton = React.forwardRef<HTMLButtonElement, GlassButtonProps>(
  ({
    className,
    variant = "default",
    size = "default",
    loading = false,
    leftIcon,
    rightIcon,
    children,
    disabled,
    ...props
  }, ref) => {
    const isDisabled = disabled || loading

    return (
      <button
        ref={ref}
        className={cn(
          glassButtonVariants({ variant, size }),
          "group relative overflow-hidden transition-all duration-300",
          "hover:scale-[1.02] active:scale-[0.98]",
          "focus:outline-none focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 focus:ring-offset-transparent",
          isDisabled && "opacity-50 cursor-not-allowed hover:scale-100",
          className
        )}
        disabled={isDisabled}
        {...props}
      >
        {/* 背景光效 */}
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000" />

        <div className="relative flex items-center justify-center space-x-2 z-10">
          {leftIcon && !loading && (
            <span className="flex-shrink-0 transition-transform duration-200 group-hover:scale-110">
              {leftIcon}
            </span>
          )}

          {loading ? (
            <div className="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent" />
          ) : (
            <span className="font-medium transition-all duration-200">
              {children}
            </span>
          )}

          {rightIcon && !loading && (
            <span className="flex-shrink-0 transition-transform duration-200 group-hover:scale-110 group-hover:translate-x-1">
              {rightIcon}
            </span>
          )}
        </div>
      </button>
    )
  }
)
GlassButton.displayName = "GlassButton"

// 玻璃拟态输入框组件
export interface GlassInputProps 
  extends React.InputHTMLAttributes<HTMLInputElement> {
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  error?: string
  label?: string
}

const GlassInput = React.forwardRef<HTMLInputElement, GlassInputProps>(
  ({ 
    className, 
    type = "text",
    leftIcon,
    rightIcon,
    error,
    label,
    id,
    ...props 
  }, ref) => {
    const inputId = id || React.useId()
    
    return (
      <div className="space-y-2">
        {label && (
          <label 
            htmlFor={inputId}
            className="block text-sm font-medium text-white/90"
          >
            {label}
          </label>
        )}
        
        <div className="relative">
          {leftIcon && (
            <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
              <div className="w-5 h-5 text-white/50">
                {leftIcon}
              </div>
            </div>
          )}
          
          <input
            type={type}
            id={inputId}
            className={cn(
              "w-full px-4 py-4 glass rounded-2xl text-white placeholder:text-white/50 border-0 focus:ring-2 focus:ring-white/30 transition-all duration-300 focus:scale-[1.02]",
              leftIcon && "pl-12",
              rightIcon && "pr-12",
              error && "ring-2 ring-red-400/50",
              className
            )}
            ref={ref}
            {...props}
          />
          
          {rightIcon && (
            <div className="absolute inset-y-0 right-0 pr-4 flex items-center pointer-events-none">
              <div className="w-5 h-5 text-white/50">
                {rightIcon}
              </div>
            </div>
          )}
        </div>
        
        {error && (
          <p className="text-xs text-red-300">
            {error}
          </p>
        )}
      </div>
    )
  }
)
GlassInput.displayName = "GlassInput"

export { 
  Glass, 
  GlassContainer, 
  GlassCard, 
  GlassButton, 
  GlassInput,
  glassVariants,
  glassButtonVariants 
}
