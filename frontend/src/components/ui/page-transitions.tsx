"use client"

import * as React from "react"
import { motion, AnimatePresence, Variants } from "framer-motion"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"

// 页面转场变体
const pageVariants: Record<string, Variants> = {
  // 淡入淡出
  fade: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 },
  },
  
  // 滑动效果
  slide: {
    initial: { x: "100%", opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: "-100%", opacity: 0 },
  },
  
  // 缩放效果
  scale: {
    initial: { scale: 0.8, opacity: 0 },
    animate: { scale: 1, opacity: 1 },
    exit: { scale: 1.2, opacity: 0 },
  },
  
  // 旋转效果
  rotate: {
    initial: { rotateY: -90, opacity: 0 },
    animate: { rotateY: 0, opacity: 1 },
    exit: { rotateY: 90, opacity: 0 },
  },
  
  // 弹性效果
  spring: {
    initial: { y: 60, opacity: 0, scale: 0.3 },
    animate: { 
      y: 0, 
      opacity: 1, 
      scale: 1,
      transition: {
        type: "spring",
        damping: 25,
        stiffness: 500,
      }
    },
    exit: { y: -60, opacity: 0, scale: 0.3 },
  },
  
  // 翻页效果
  flip: {
    initial: { rotateX: -90, opacity: 0 },
    animate: { 
      rotateX: 0, 
      opacity: 1,
      transition: {
        type: "spring",
        damping: 20,
        stiffness: 300,
      }
    },
    exit: { rotateX: 90, opacity: 0 },
  },
  
  // 玻璃破碎效果
  shatter: {
    initial: { 
      scale: 0.8, 
      opacity: 0,
      filter: "blur(10px)",
    },
    animate: { 
      scale: 1, 
      opacity: 1,
      filter: "blur(0px)",
      transition: {
        duration: 0.6,
        ease: [0.25, 0.25, 0, 1],
      }
    },
    exit: { 
      scale: 1.1, 
      opacity: 0,
      filter: "blur(5px)",
    },
  },
}

// 页面转场组件
export interface PageTransitionProps {
  children: React.ReactNode
  variant?: keyof typeof pageVariants
  duration?: number
  className?: string
  preserveScroll?: boolean
}

export const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  variant = "fade",
  duration = 0.3,
  className,
  preserveScroll = false,
}) => {
  const pathname = usePathname()
  const [scrollPosition, setScrollPosition] = React.useState(0)

  // 保存滚动位置
  React.useEffect(() => {
    if (preserveScroll) {
      const handleScroll = () => setScrollPosition(window.scrollY)
      window.addEventListener('scroll', handleScroll)
      return () => window.removeEventListener('scroll', handleScroll)
    }
  }, [preserveScroll])

  // 恢复滚动位置
  React.useEffect(() => {
    if (preserveScroll) {
      window.scrollTo(0, scrollPosition)
    }
  }, [pathname, scrollPosition, preserveScroll])

  const variants = pageVariants[variant]

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={pathname}
        initial="initial"
        animate="animate"
        exit="exit"
        variants={variants}
        transition={{
          duration,
          ease: [0.25, 0.25, 0, 1],
        }}
        className={cn("w-full", className)}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  )
}

// 路由转场包装器
export interface RouteTransitionProps {
  children: React.ReactNode
  variant?: keyof typeof pageVariants
  className?: string
}

export const RouteTransition: React.FC<RouteTransitionProps> = ({
  children,
  variant = "fade",
  className,
}) => {
  const pathname = usePathname()

  return (
    <AnimatePresence mode="wait">
      <motion.div
        key={pathname}
        initial="initial"
        animate="animate"
        exit="exit"
        variants={pageVariants[variant]}
        transition={{
          duration: 0.3,
          ease: [0.25, 0.25, 0, 1],
        }}
        className={className}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  )
}

// 模态框转场动画
export const modalVariants: Variants = {
  hidden: {
    opacity: 0,
    scale: 0.8,
    y: 50,
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      type: "spring",
      damping: 25,
      stiffness: 500,
    },
  },
  exit: {
    opacity: 0,
    scale: 0.8,
    y: 50,
    transition: {
      duration: 0.2,
    },
  },
}

// 侧边栏转场动画
export const sidebarVariants: Variants = {
  closed: {
    x: "-100%",
    transition: {
      type: "spring",
      damping: 25,
      stiffness: 300,
    },
  },
  open: {
    x: 0,
    transition: {
      type: "spring",
      damping: 25,
      stiffness: 300,
    },
  },
}

// 下拉菜单转场动画
export const dropdownVariants: Variants = {
  closed: {
    opacity: 0,
    scale: 0.95,
    y: -10,
    transition: {
      duration: 0.1,
    },
  },
  open: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      duration: 0.2,
      ease: [0.25, 0.25, 0, 1],
    },
  },
}

// 工具提示转场动画
export const tooltipVariants: Variants = {
  hidden: {
    opacity: 0,
    scale: 0.8,
    y: 5,
  },
  visible: {
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      duration: 0.15,
      ease: "easeOut",
    },
  },
  exit: {
    opacity: 0,
    scale: 0.8,
    y: 5,
    transition: {
      duration: 0.1,
    },
  },
}

// 列表项转场动画
export const listItemVariants: Variants = {
  hidden: {
    opacity: 0,
    x: -20,
  },
  visible: (index: number) => ({
    opacity: 1,
    x: 0,
    transition: {
      delay: index * 0.1,
      duration: 0.3,
      ease: [0.25, 0.25, 0, 1],
    },
  }),
  exit: {
    opacity: 0,
    x: 20,
    transition: {
      duration: 0.2,
    },
  },
}

// 卡片悬停动画
export const cardHoverVariants: Variants = {
  rest: {
    scale: 1,
    y: 0,
    transition: {
      duration: 0.2,
      ease: "easeOut",
    },
  },
  hover: {
    scale: 1.02,
    y: -4,
    transition: {
      duration: 0.2,
      ease: "easeOut",
    },
  },
  tap: {
    scale: 0.98,
    y: 0,
    transition: {
      duration: 0.1,
    },
  },
}

// 按钮点击动画
export const buttonVariants: Variants = {
  rest: {
    scale: 1,
  },
  hover: {
    scale: 1.05,
    transition: {
      duration: 0.2,
      ease: "easeOut",
    },
  },
  tap: {
    scale: 0.95,
    transition: {
      duration: 0.1,
    },
  },
}

// 加载动画变体
export const loadingVariants: Variants = {
  start: {
    rotate: 0,
  },
  end: {
    rotate: 360,
    transition: {
      duration: 1,
      repeat: Infinity,
      ease: "linear",
    },
  },
}

// 通知动画变体
export const notificationVariants: Variants = {
  hidden: {
    opacity: 0,
    y: -50,
    scale: 0.3,
  },
  visible: {
    opacity: 1,
    y: 0,
    scale: 1,
    transition: {
      type: "spring",
      damping: 25,
      stiffness: 500,
    },
  },
  exit: {
    opacity: 0,
    y: -50,
    scale: 0.3,
    transition: {
      duration: 0.2,
    },
  },
}

// 自定义缓动函数
export const customEasing = {
  easeInOutCubic: [0.645, 0.045, 0.355, 1],
  easeInOutQuart: [0.77, 0, 0.175, 1],
  easeInOutQuint: [0.86, 0, 0.07, 1],
  easeInOutExpo: [0.87, 0, 0.13, 1],
  easeInOutCirc: [0.85, 0, 0.15, 1],
  easeInOutBack: [0.68, -0.6, 0.32, 1.6],
} as const
