"use client"

import * as React from "react"
import { motion, AnimatePresence } from "framer-motion"
import { <PERSON>ader2, <PERSON>ert<PERSON>ircle, RefreshCw } from "lucide-react"
import { EnhancedGlassCard } from "./enhanced-glass"
import { GlassButton } from "./glass"
import { cn } from "@/lib/utils"

// 懒加载包装器组件
export interface LazyWrapperProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  error?: React.ReactNode
  className?: string
  delay?: number
}

export const LazyWrapper: React.FC<LazyWrapperProps> = ({
  children,
  fallback,
  error,
  className,
  delay = 0,
}) => {
  const [isLoaded, setIsLoaded] = React.useState(false)
  const [hasError, setHasError] = React.useState(false)

  React.useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoaded(true)
    }, delay)

    return () => clearTimeout(timer)
  }, [delay])

  if (hasError && error) {
    return <div className={className}>{error}</div>
  }

  if (!isLoaded && fallback) {
    return <div className={className}>{fallback}</div>
  }

  return (
    <div className={className}>
      <React.Suspense
        fallback={fallback || <LoadingSpinner />}
      >
        <ErrorBoundary
          fallback={error || <ErrorFallback onRetry={() => setHasError(false)} />}
          onError={() => setHasError(true)}
        >
          {children}
        </ErrorBoundary>
      </React.Suspense>
    </div>
  )
}

// 加载动画组件
export interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg"
  text?: string
  className?: string
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = "md",
  text = "加载中...",
  className,
}) => {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-6 h-6",
    lg: "w-8 h-8",
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className={cn(
        "flex flex-col items-center justify-center p-8 space-y-4",
        className
      )}
    >
      <Loader2 className={cn("animate-spin text-primary", sizeClasses[size])} />
      {text && (
        <p className="text-sm text-white/70 animate-pulse">{text}</p>
      )}
    </motion.div>
  )
}

// 骨架屏组件
export interface SkeletonProps {
  className?: string
  variant?: "text" | "circular" | "rectangular"
  width?: string | number
  height?: string | number
  animation?: "pulse" | "wave" | "none"
}

export const Skeleton: React.FC<SkeletonProps> = ({
  className,
  variant = "rectangular",
  width,
  height,
  animation = "pulse",
}) => {
  const variantClasses = {
    text: "rounded",
    circular: "rounded-full",
    rectangular: "rounded-lg",
  }

  const animationClasses = {
    pulse: "animate-pulse",
    wave: "animate-shimmer",
    none: "",
  }

  const style: React.CSSProperties = {}
  if (width) style.width = typeof width === "number" ? `${width}px` : width
  if (height) style.height = typeof height === "number" ? `${height}px` : height

  return (
    <div
      className={cn(
        "bg-white/10 backdrop-blur-sm",
        variantClasses[variant],
        animationClasses[animation],
        className
      )}
      style={style}
    />
  )
}

// 骨架屏布局组件
export const SkeletonCard: React.FC<{ className?: string }> = ({ className }) => (
  <EnhancedGlassCard variant="minimal" className={cn("p-6 space-y-4", className)}>
    <div className="flex items-center space-x-4">
      <Skeleton variant="circular" width={48} height={48} />
      <div className="flex-1 space-y-2">
        <Skeleton variant="text" height={20} width="60%" />
        <Skeleton variant="text" height={16} width="40%" />
      </div>
    </div>
    <div className="space-y-2">
      <Skeleton variant="text" height={16} width="100%" />
      <Skeleton variant="text" height={16} width="80%" />
      <Skeleton variant="text" height={16} width="90%" />
    </div>
    <div className="flex space-x-2">
      <Skeleton variant="rectangular" height={36} width={80} />
      <Skeleton variant="rectangular" height={36} width={80} />
    </div>
  </EnhancedGlassCard>
)

// 错误边界组件
interface ErrorBoundaryProps {
  children: React.ReactNode
  fallback: React.ReactNode
  onError?: () => void
}

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    this.props.onError?.()
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback
    }

    return this.props.children
  }
}

// 错误回退组件
export interface ErrorFallbackProps {
  onRetry?: () => void
  error?: Error
  className?: string
}

export const ErrorFallback: React.FC<ErrorFallbackProps> = ({
  onRetry,
  error,
  className,
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className={cn(
        "flex flex-col items-center justify-center p-8 space-y-4 text-center",
        className
      )}
    >
      <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center">
        <AlertCircle className="w-8 h-8 text-red-400" />
      </div>
      
      <div className="space-y-2">
        <h3 className="text-lg font-semibold text-white">加载失败</h3>
        <p className="text-sm text-white/70 max-w-md">
          {error?.message || "抱歉，内容加载时出现了问题。请稍后重试。"}
        </p>
      </div>

      {onRetry && (
        <GlassButton
          variant="subtle"
          onClick={onRetry}
          leftIcon={<RefreshCw className="w-4 h-4" />}
          className="hover:bg-red-500/20 border-red-400/30"
        >
          重试
        </GlassButton>
      )}
    </motion.div>
  )
}

// 图片懒加载组件
export interface LazyImageProps extends React.ImgHTMLAttributes<HTMLImageElement> {
  src: string
  alt: string
  placeholder?: string
  fallback?: React.ReactNode
  onLoad?: () => void
  onError?: () => void
  className?: string
}

export const LazyImage: React.FC<LazyImageProps> = ({
  src,
  alt,
  placeholder,
  fallback,
  onLoad,
  onError,
  className,
  ...props
}) => {
  const [isLoaded, setIsLoaded] = React.useState(false)
  const [hasError, setHasError] = React.useState(false)
  const [isInView, setIsInView] = React.useState(false)
  const imgRef = React.useRef<HTMLImageElement>(null)

  // 交叉观察器用于懒加载
  React.useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true)
          observer.disconnect()
        }
      },
      { threshold: 0.1 }
    )

    if (imgRef.current) {
      observer.observe(imgRef.current)
    }

    return () => observer.disconnect()
  }, [])

  const handleLoad = () => {
    setIsLoaded(true)
    onLoad?.()
  }

  const handleError = () => {
    setHasError(true)
    onError?.()
  }

  if (hasError && fallback) {
    return <div className={className}>{fallback}</div>
  }

  return (
    <div ref={imgRef} className={cn("relative overflow-hidden", className)}>
      <AnimatePresence>
        {!isLoaded && (
          <motion.div
            initial={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 flex items-center justify-center bg-white/5 backdrop-blur-sm"
          >
            {placeholder ? (
              <img src={placeholder} alt="" className="w-full h-full object-cover opacity-50" />
            ) : (
              <Skeleton variant="rectangular" className="w-full h-full" />
            )}
          </motion.div>
        )}
      </AnimatePresence>

      {isInView && (
        <motion.img
          src={src}
          alt={alt}
          onLoad={handleLoad}
          onError={handleError}
          initial={{ opacity: 0 }}
          animate={{ opacity: isLoaded ? 1 : 0 }}
          transition={{ duration: 0.3 }}
          className="w-full h-full object-cover"
          {...props}
        />
      )}
    </div>
  )
}

// 虚拟滚动组件（用于大列表性能优化）
export interface VirtualScrollProps<T> {
  items: T[]
  itemHeight: number
  containerHeight: number
  renderItem: (item: T, index: number) => React.ReactNode
  className?: string
  overscan?: number
}

export function VirtualScroll<T>({
  items,
  itemHeight,
  containerHeight,
  renderItem,
  className,
  overscan = 5,
}: VirtualScrollProps<T>) {
  const [scrollTop, setScrollTop] = React.useState(0)
  const scrollElementRef = React.useRef<HTMLDivElement>(null)

  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  )

  const visibleItems = items.slice(startIndex, endIndex + 1)

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop)
  }

  return (
    <div
      ref={scrollElementRef}
      className={cn("overflow-auto", className)}
      style={{ height: containerHeight }}
      onScroll={handleScroll}
    >
      <div style={{ height: items.length * itemHeight, position: 'relative' }}>
        {visibleItems.map((item, index) => (
          <div
            key={startIndex + index}
            style={{
              position: 'absolute',
              top: (startIndex + index) * itemHeight,
              height: itemHeight,
              width: '100%',
            }}
          >
            {renderItem(item, startIndex + index)}
          </div>
        ))}
      </div>
    </div>
  )
}
