"use client"

import * as React from "react"
import { motion } from "framer-motion"
import { UserPlus, User, Mail, Lock, ShieldCheck, ArrowLeft } from "lucide-react"
import { GlassCard, GlassInput, GlassButton } from "@/components/ui/glass"
import { EnhancedGlassCard } from "@/components/ui/enhanced-glass"
import { <PERSON>adeIn, <PERSON>agger } from "@/components/ui/animated-elements"
import { isValidEmail } from "@/lib/utils"

export interface RegisterFormProps {
  onSubmit: (data: {
    username: string
    email: string
    password: string
    confirmPassword: string
  }) => Promise<void>
  onBack: () => void
  onSwitchToLogin: () => void
  isLoading?: boolean
  error?: string
}

export function RegisterForm({ 
  onSubmit, 
  onBack, 
  onSwitchToLogin, 
  isLoading = false,
  error 
}: RegisterFormProps) {
  const [formData, setFormData] = React.useState({
    username: "",
    email: "",
    password: "",
    confirmPassword: ""
  })
  const [formErrors, setFormErrors] = React.useState<Record<string, string>>({})

  const validateForm = () => {
    const errors: Record<string, string> = {}
    
    if (!formData.username.trim()) {
      errors.username = "请输入用户名"
    } else if (formData.username.length < 3) {
      errors.username = "用户名至少需要3个字符"
    } else if (formData.username.length > 20) {
      errors.username = "用户名不能超过20个字符"
    }
    
    if (!formData.email.trim()) {
      errors.email = "请输入邮箱地址"
    } else if (!isValidEmail(formData.email)) {
      errors.email = "请输入有效的邮箱地址"
    }
    
    if (!formData.password) {
      errors.password = "请输入密码"
    } else if (formData.password.length < 6) {
      errors.password = "密码至少需要6个字符"
    }
    
    if (!formData.confirmPassword) {
      errors.confirmPassword = "请确认密码"
    } else if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = "两次输入的密码不一致"
    }
    
    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }
    
    try {
      await onSubmit(formData)
    } catch (error) {
      console.error("注册失败:", error)
    }
  }

  const handleInputChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [field]: e.target.value
    }))
    
    // 清除对应字段的错误
    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: ""
      }))
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <GlassCard
        variant="strong"
        hover="all"
        className="w-full max-w-lg mx-auto"
        title="用户注册"
        description="创建您的账户以开始使用"
        icon={<UserPlus className="w-6 h-6 text-white" />}
      >
        <form onSubmit={handleSubmit} className="space-y-6">
          <GlassInput
            label="用户名"
            type="text"
            placeholder="请设置您的用户名"
            value={formData.username}
            onChange={handleInputChange("username")}
            leftIcon={<User className="w-5 h-5" />}
            error={formErrors.username}
            disabled={isLoading}
            required
          />

          <GlassInput
            label="电子邮箱"
            type="email"
            placeholder="请输入您的电子邮箱"
            value={formData.email}
            onChange={handleInputChange("email")}
            leftIcon={<Mail className="w-5 h-5" />}
            error={formErrors.email}
            disabled={isLoading}
            required
          />

          <GlassInput
            label="密码"
            type="password"
            placeholder="请设置您的密码"
            value={formData.password}
            onChange={handleInputChange("password")}
            leftIcon={<Lock className="w-5 h-5" />}
            error={formErrors.password}
            disabled={isLoading}
            required
          />

          <GlassInput
            label="确认密码"
            type="password"
            placeholder="请再次输入密码"
            value={formData.confirmPassword}
            onChange={handleInputChange("confirmPassword")}
            leftIcon={<ShieldCheck className="w-5 h-5" />}
            error={formErrors.confirmPassword}
            disabled={isLoading}
            required
          />

          {error && (
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="p-4 bg-red-500/20 border border-red-500/30 rounded-2xl text-red-200"
            >
              {error}
            </motion.div>
          )}

          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <GlassButton
              type="submit"
              variant="strong"
              size="lg"
              loading={isLoading}
              className="flex-1 bg-gradient-to-r from-indigo-500/20 to-blue-600/20 hover:from-indigo-500/30 hover:to-blue-600/30"
              rightIcon={!isLoading && <UserPlus className="w-5 h-5" />}
            >
              {isLoading ? "注册中..." : "注册"}
            </GlassButton>
            
            <GlassButton
              type="button"
              variant="subtle"
              size="lg"
              onClick={onBack}
              disabled={isLoading}
              leftIcon={<ArrowLeft className="w-5 h-5" />}
            >
              返回
            </GlassButton>
          </div>

          <div className="text-center mt-6">
            <p className="text-white/70">
              已有账户？{" "}
              <button
                type="button"
                onClick={onSwitchToLogin}
                className="text-white font-semibold hover:text-white/80 transition-colors underline underline-offset-4"
                disabled={isLoading}
              >
                立即登录
              </button>
            </p>
          </div>
        </form>
      </GlassCard>
    </motion.div>
  )
}
