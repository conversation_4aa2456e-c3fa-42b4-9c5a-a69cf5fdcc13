"use client"

import * as React from "react"
import { motion } from "framer-motion"
import { LogIn, User, Lock, ArrowLeft } from "lucide-react"
import { GlassCard, GlassInput, GlassButton } from "@/components/ui/glass"
import { EnhancedGlassCard } from "@/components/ui/enhanced-glass"
import { <PERSON>adeIn, <PERSON>agger } from "@/components/ui/animated-elements"
import { cn } from "@/lib/utils"

export interface LoginFormProps {
  onSubmit: (data: { username: string; password: string }) => Promise<void>
  onBack: () => void
  onSwitchToRegister: () => void
  isLoading?: boolean
  error?: string
}

export function LoginForm({ 
  onSubmit, 
  onBack, 
  onSwitchToRegister, 
  isLoading = false,
  error 
}: LoginFormProps) {
  const [formData, setFormData] = React.useState({
    username: "",
    password: ""
  })
  const [formErrors, setFormErrors] = React.useState<Record<string, string>>({})

  const validateForm = () => {
    const errors: Record<string, string> = {}
    
    if (!formData.username.trim()) {
      errors.username = "请输入用户名"
    }
    
    if (!formData.password) {
      errors.password = "请输入密码"
    }
    
    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }
    
    try {
      await onSubmit(formData)
    } catch (error) {
      console.error("登录失败:", error)
    }
  }

  const handleInputChange = (field: string) => (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [field]: e.target.value
    }))
    
    // 清除对应字段的错误
    if (formErrors[field]) {
      setFormErrors(prev => ({
        ...prev,
        [field]: ""
      }))
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <EnhancedGlassCard
        variant="ocean"
        size="lg"
        glow="subtle"
        floating
        className="w-full max-w-lg mx-auto"
      >
        <FadeIn delay={0.1} className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-500/20 to-cyan-500/20 rounded-2xl mb-4">
            <LogIn className="w-6 h-6 text-white" />
          </div>
          <h2 className="text-2xl font-semibold text-white mb-2 bg-gradient-to-r from-white to-blue-100 bg-clip-text">
            用户登录
          </h2>
          <p className="text-white/70">登录您的账户以管理订单</p>
        </FadeIn>

        <form onSubmit={handleSubmit}>
          <Stagger staggerDelay={0.1} className="space-y-6">
            <GlassInput
              label="用户名"
              type="text"
              placeholder="请输入您的用户名"
              value={formData.username}
              onChange={handleInputChange("username")}
              leftIcon={<User className="w-5 h-5" />}
              error={formErrors.username}
              disabled={isLoading}
              required
            />

            <GlassInput
              label="密码"
              type="password"
              placeholder="请输入您的密码"
              value={formData.password}
              onChange={handleInputChange("password")}
              leftIcon={<Lock className="w-5 h-5" />}
              error={formErrors.password}
              disabled={isLoading}
              required
            />

            {error && (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                className="p-4 bg-gradient-to-r from-red-500/20 to-pink-500/20 border border-red-400/30 rounded-2xl text-red-200 backdrop-blur-sm"
              >
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-red-400 rounded-full animate-pulse" />
                  <span>{error}</span>
                </div>
              </motion.div>
            )}

            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <GlassButton
                type="submit"
                variant="strong"
                size="lg"
                loading={isLoading}
                className="flex-1 bg-gradient-to-r from-blue-500/20 to-cyan-500/20 hover:from-blue-500/30 hover:to-cyan-500/30 border-blue-400/30"
                rightIcon={!isLoading && <LogIn className="w-5 h-5" />}
              >
                {isLoading ? "登录中..." : "登录"}
              </GlassButton>

              <GlassButton
                type="button"
                variant="subtle"
                size="lg"
                onClick={onBack}
                disabled={isLoading}
                leftIcon={<ArrowLeft className="w-5 h-5" />}
                className="hover:bg-white/10"
              >
                返回
              </GlassButton>
            </div>

            <div className="text-center mt-6">
              <p className="text-white/70">
                还没有账户？{" "}
                <button
                  type="button"
                  onClick={onSwitchToRegister}
                  className="text-cyan-300 font-semibold hover:text-cyan-200 transition-all duration-300 underline underline-offset-4 hover:underline-offset-2"
                  disabled={isLoading}
                >
                  立即注册
                </button>
              </p>
            </div>
          </Stagger>
        </form>
      </EnhancedGlassCard>
    </motion.div>
  )
}
