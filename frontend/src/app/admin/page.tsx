"use client"

import * as React from "react"
import { AnimatePresence, motion } from "framer-motion"
import { Shield, LogOut } from "lucide-react"
import { Background } from "@/components/ui/background"
import { GlassButton } from "@/components/ui/glass"
import { EnhancedGlassCard } from "@/components/ui/enhanced-glass"
import { FadeIn, Floating } from "@/components/ui/animated-elements"
import { AdminLogin } from "@/components/admin/admin-login"
import { AdminPanel } from "@/components/admin/admin-panel"
import type { Order, BindingData } from "@/types"

type ViewType = 'login' | 'panel'

export default function AdminPage() {
  const [currentView, setCurrentView] = React.useState<ViewType>('login')
  const [bindings, setBindings] = React.useState<Order[]>([])
  const [isLoading, setIsLoading] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)
  const [isLoadingBindings, setIsLoadingBindings] = React.useState(false)

  // 检查管理员登录状态
  React.useEffect(() => {
    const adminToken = localStorage.getItem('adminToken')
    if (adminToken) {
      setCurrentView('panel')
      fetchBindings()
    }
  }, [])

  // 获取绑定数据
  const fetchBindings = async () => {
    setIsLoadingBindings(true)
    try {
      const response = await fetch('/admin/bindings', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,
          'Content-Type': 'application/json'
        }
      })

      const result = await response.json()
      if (result.success) {
        setBindings(result.data || [])
      } else {
        throw new Error(result.message || '获取数据失败')
      }
    } catch (error) {
      console.error('获取绑定数据失败:', error)
      setError(error instanceof Error ? error.message : '获取数据失败')
    } finally {
      setIsLoadingBindings(false)
    }
  }

  // 管理员登录
  const handleAdminLogin = async (password: string) => {
    setIsLoading(true)
    setError(null)
    
    try {
      const response = await fetch('/admin/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ password })
      })

      const result = await response.json()
      
      if (result.success) {
        localStorage.setItem('adminToken', result.token || 'admin-authenticated')
        setCurrentView('panel')
        await fetchBindings()
      } else {
        throw new Error(result.message || '密码错误')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '登录失败')
    } finally {
      setIsLoading(false)
    }
  }

  // 添加绑定
  const handleAddBinding = async (data: BindingData) => {
    const response = await fetch('/admin/bindings', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    })

    const result = await response.json()
    if (result.success) {
      await fetchBindings()
    } else {
      throw new Error(result.message || '添加失败')
    }
  }

  // 编辑绑定
  const handleEditBinding = async (data: BindingData & { originalOrderNumber: string; originalSkuId: string }) => {
    const response = await fetch('/admin/bindings', {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    })

    const result = await response.json()
    if (result.success) {
      await fetchBindings()
    } else {
      throw new Error(result.message || '更新失败')
    }
  }

  // 删除绑定
  const handleDeleteBinding = async (orderNumber: string, skuId: string) => {
    const response = await fetch('/admin/bindings', {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ orderNumber, skuId })
    })

    const result = await response.json()
    if (result.success) {
      await fetchBindings()
    } else {
      throw new Error(result.message || '删除失败')
    }
  }

  // 升级绑定
  const handleUpgradeBinding = async (id: number) => {
    const response = await fetch('/admin/upgrade', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('adminToken')}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ id })
    })

    const result = await response.json()
    if (result.success) {
      await fetchBindings()
    } else {
      throw new Error(result.message || '升级失败')
    }
  }

  // 退出登录
  const handleLogout = () => {
    localStorage.removeItem('adminToken')
    setCurrentView('login')
    setBindings([])
    setError(null)
  }

  return (
    <Background variant="admin">
      <div className="min-h-screen p-4">
        {/* 头部导航 */}
        <FadeIn delay={0.2} className="flex justify-between items-center mb-8">
          <div className="flex items-center space-x-4">
            <Floating intensity="subtle" speed="slow">
              <div className="w-12 h-12 bg-gradient-to-br from-red-500/20 to-orange-500/20 rounded-xl flex items-center justify-center backdrop-blur-sm shadow-lg shadow-red-500/20">
                <Shield className="w-6 h-6 text-white" />
              </div>
            </Floating>
            <div>
              <motion.h1
                className="text-3xl font-bold text-white bg-gradient-to-r from-white via-red-100 to-orange-100 bg-clip-text"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.4, duration: 0.6 }}
              >
                管理后台
              </motion.h1>
              <motion.p
                className="text-white/70"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6, duration: 0.6 }}
              >
                系统管理与数据维护
              </motion.p>
            </div>
          </div>

          {currentView === 'panel' && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.8, duration: 0.5 }}
            >
              <GlassButton
                variant="subtle"
                onClick={handleLogout}
                leftIcon={<LogOut className="w-4 h-4" />}
                className="hover:bg-red-500/20 border-red-400/30"
              >
                退出登录
              </GlassButton>
            </motion.div>
          )}
        </FadeIn>

        {/* 主要内容区域 */}
        <div className="max-w-7xl mx-auto">
          <AnimatePresence mode="wait">
            {currentView === 'login' && (
              <AdminLogin
                onSubmit={handleAdminLogin}
                isLoading={isLoading}
                error={error}
              />
            )}
            
            {currentView === 'panel' && (
              <AdminPanel
                bindings={bindings}
                isLoading={isLoadingBindings}
                onRefresh={fetchBindings}
                onAddBinding={handleAddBinding}
                onEditBinding={handleEditBinding}
                onDeleteBinding={handleDeleteBinding}
                onUpgradeBinding={handleUpgradeBinding}
              />
            )}
          </AnimatePresence>
        </div>
      </div>
    </Background>
  )
}
