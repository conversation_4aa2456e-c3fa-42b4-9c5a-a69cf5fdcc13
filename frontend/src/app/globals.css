@import "tailwindcss";

/* 苹果风格设计系统 */
:root {
  /* 基础颜色 */
  --background: #ffffff;
  --foreground: #1d1d1f;
  --muted: #f5f5f7;
  --muted-foreground: #86868b;
  --border: #d2d2d7;
  --input: #ffffff;

  /* 主题色 - iOS风格 */
  --primary: #007aff;
  --primary-foreground: #ffffff;
  --secondary: #f5f5f7;
  --secondary-foreground: #1d1d1f;
  --accent: #007aff;
  --accent-foreground: #ffffff;
  --destructive: #ff3b30;
  --destructive-foreground: #ffffff;
  --success: #34c759;
  --warning: #ff9500;
  --ring: #007aff;

  /* 玻璃拟态效果变量 */
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-strong-bg: rgba(255, 255, 255, 0.15);
  --glass-strong-border: rgba(255, 255, 255, 0.25);
  --glass-dark-bg: rgba(0, 0, 0, 0.2);
  --glass-dark-border: rgba(255, 255, 255, 0.1);
  --glass-subtle-bg: rgba(255, 255, 255, 0.05);
  --glass-subtle-border: rgba(255, 255, 255, 0.1);

  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  /* 动画时长 */
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;

  /* 缓动函数 */
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-success: var(--success);
  --color-warning: var(--warning);
  --color-ring: var(--ring);

  --color-glass: var(--glass-bg);
  --color-glass-border: var(--glass-border);
  --color-glass-strong: var(--glass-strong-bg);
  --color-glass-strong-border: var(--glass-strong-border);
  --color-glass-dark: var(--glass-dark-bg);
  --color-glass-dark-border: var(--glass-dark-border);
  --color-glass-subtle: var(--glass-subtle-bg);
  --color-glass-subtle-border: var(--glass-subtle-border);

  --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;

  /* 自定义阴影 */
  --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
  --shadow-glass-lg: 0 15px 35px 0 rgba(31, 38, 135, 0.4);

  /* 渐变背景 */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #000000;
    --foreground: #ffffff;
    --muted: #1c1c1e;
    --muted-foreground: #8e8e93;
    --border: #38383a;
    --input: #1c1c1e;
    --primary: #0a84ff;
    --primary-foreground: #ffffff;
    --secondary: #1c1c1e;
    --secondary-foreground: #ffffff;
    --accent: #0a84ff;
    --accent-foreground: #ffffff;
    --destructive: #ff453a;
    --destructive-foreground: #ffffff;
    --success: #30d158;
    --warning: #ff9f0a;
    --ring: #0a84ff;

    /* 暗色模式下的玻璃效果 */
    --glass-bg: rgba(255, 255, 255, 0.05);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-strong-bg: rgba(255, 255, 255, 0.1);
    --glass-strong-border: rgba(255, 255, 255, 0.15);
    --glass-dark-bg: rgba(0, 0, 0, 0.3);
    --glass-dark-border: rgba(255, 255, 255, 0.05);
    --glass-subtle-bg: rgba(255, 255, 255, 0.02);
    --glass-subtle-border: rgba(255, 255, 255, 0.05);
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  :root {
    --glass-bg: rgba(255, 255, 255, 0.2);
    --glass-border: rgba(255, 255, 255, 0.4);
    --glass-strong-bg: rgba(255, 255, 255, 0.3);
    --glass-strong-border: rgba(255, 255, 255, 0.5);
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .animate-float,
  .animate-spin,
  .animate-pulse {
    animation: none !important;
  }
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  -webkit-text-size-adjust: 100%;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  font-feature-settings: "rlig" 1, "calt" 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  line-height: 1.5;
  min-height: 100vh;
}

.glass {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
}

.glass-strong {
  background: var(--glass-strong-bg);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  border: 1px solid var(--glass-strong-border);
}

.glass-dark {
  background: var(--glass-dark-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-dark-border);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% { 
    transform: translateY(0px) rotate(0deg); 
  }
  33% { 
    transform: translateY(-10px) rotate(1deg); 
  }
  66% { 
    transform: translateY(5px) rotate(-1deg); 
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 5px rgba(0, 122, 255, 0.3), 0 0 10px rgba(0, 122, 255, 0.2), 0 0 15px rgba(0, 122, 255, 0.1);
  }
  50% {
    box-shadow: 0 0 10px rgba(0, 122, 255, 0.4), 0 0 20px rgba(0, 122, 255, 0.3), 0 0 30px rgba(0, 122, 255, 0.2);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }
  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

@keyframes wiggle {
  0%, 100% { transform: rotate(-3deg); }
  50% { transform: rotate(3deg); }
}

@keyframes heartbeat {
  0% { transform: scale(1); }
  14% { transform: scale(1.3); }
  28% { transform: scale(1); }
  42% { transform: scale(1.3); }
  70% { transform: scale(1); }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--primary), 0 0 10px var(--primary), 0 0 15px var(--primary);
  }
  50% {
    box-shadow: 0 0 10px var(--primary), 0 0 20px var(--primary), 0 0 30px var(--primary);
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out;
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

.animate-glow {
  animation: glow-pulse 2s ease-in-out infinite;
}

.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.animate-spin {
  animation: spin 1s linear infinite;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-shimmer {
  animation: shimmer 2s infinite;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  background-size: 200px 100%;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

.animate-wiggle {
  animation: wiggle 0.5s ease-in-out infinite;
}

.animate-heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

/* 悬停效果增强 */
.hover-lift {
  transition: transform var(--duration-normal) var(--ease-out),
              box-shadow var(--duration-normal) var(--ease-out);
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.hover-scale {
  transition: transform var(--duration-normal) var(--ease-spring);
}

.hover-scale:hover {
  transform: scale(1.05);
}

.hover-glow {
  transition: box-shadow var(--duration-normal) var(--ease-out);
}

.hover-glow:hover {
  box-shadow: 0 0 20px rgba(0, 122, 255, 0.3);
}

/* 移动端优化 */
@media (max-width: 640px) {
  /* 玻璃效果性能优化 */
  .glass, .glass-strong, .glass-dark {
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
  }

  .glass {
    background: rgba(255, 255, 255, 0.15);
  }

  .glass-strong {
    background: rgba(255, 255, 255, 0.2);
  }

  /* 字体大小优化 */
  h1 { font-size: 2rem; line-height: 1.2; }
  h2 { font-size: 1.5rem; line-height: 1.3; }
  h3 { font-size: 1.25rem; line-height: 1.4; }

  /* 触摸目标优化 */
  button, input, textarea, select {
    min-height: 44px;
    font-size: 16px; /* 防止iOS缩放 */
    touch-action: manipulation;
  }

  /* 触摸反馈 */
  button:active, .glass-button:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }

  /* 滚动优化 */
  * {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* 防止水平滚动 */
  body {
    overflow-x: hidden;
  }

  /* 间距调整 */
  .space-y-8 > * + * { margin-top: 1.5rem; }
  .space-y-6 > * + * { margin-top: 1rem; }
  .space-y-4 > * + * { margin-top: 0.75rem; }

  /* 表格移动端优化 */
  table {
    font-size: 0.875rem;
  }

  th, td {
    padding: 0.5rem !important;
  }
}

/* 平板端优化 */
@media (min-width: 641px) and (max-width: 1024px) {
  /* 中等屏幕的玻璃效果 */
  .glass, .glass-strong, .glass-dark {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
  }

  /* 触摸目标保持合适大小 */
  button, input, textarea, select {
    min-height: 40px;
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .glass, .glass-strong, .glass-dark {
    backdrop-filter: blur(25px);
    -webkit-backdrop-filter: blur(25px);
  }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 500px) {
  /* 减少垂直间距 */
  .space-y-8 > * + * { margin-top: 1rem; }
  .space-y-6 > * + * { margin-top: 0.75rem; }
  .space-y-4 > * + * { margin-top: 0.5rem; }

  /* 调整字体大小 */
  h1 { font-size: 1.75rem; }
  h2 { font-size: 1.25rem; }
  h3 { font-size: 1.125rem; }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--primary);
  color: var(--primary-foreground);
  padding: 8px;
  border-radius: 4px;
  text-decoration: none;
  z-index: 1000;
  transition: top 0.3s;
}

.skip-link:focus {
  top: 6px;
}

/* 高对比度模式 */
.high-contrast {
  --glass-bg: rgba(255, 255, 255, 0.3);
  --glass-border: rgba(255, 255, 255, 0.6);
  --glass-strong-bg: rgba(255, 255, 255, 0.4);
  --glass-strong-border: rgba(255, 255, 255, 0.7);
}

.high-contrast .glass,
.high-contrast .glass-strong,
.high-contrast .glass-dark {
  border-width: 2px;
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.5);
}

.high-contrast button,
.high-contrast input,
.high-contrast textarea,
.high-contrast select {
  border-width: 2px;
  outline: 2px solid transparent;
}

.high-contrast button:focus,
.high-contrast input:focus,
.high-contrast textarea:focus,
.high-contrast select:focus {
  outline: 3px solid var(--primary);
  outline-offset: 2px;
}

/* 大字体模式 */
.large-text {
  font-size: 1.125rem;
}

.large-text h1 { font-size: 3rem; }
.large-text h2 { font-size: 2.25rem; }
.large-text h3 { font-size: 1.875rem; }
.large-text h4 { font-size: 1.5rem; }
.large-text h5 { font-size: 1.25rem; }
.large-text h6 { font-size: 1.125rem; }

.large-text .text-xs { font-size: 0.875rem; }
.large-text .text-sm { font-size: 1rem; }
.large-text .text-base { font-size: 1.125rem; }
.large-text .text-lg { font-size: 1.25rem; }
.large-text .text-xl { font-size: 1.5rem; }

/* 键盘导航支持 */
.keyboard-navigation *:focus {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

.keyboard-navigation button:focus,
.keyboard-navigation input:focus,
.keyboard-navigation textarea:focus,
.keyboard-navigation select:focus,
.keyboard-navigation a:focus {
  box-shadow: 0 0 0 3px rgba(var(--primary), 0.3);
}

/* 焦点可见性 */
.focus-visible *:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* 减少动画模式 */
.reduced-motion *,
.reduced-motion *::before,
.reduced-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

.reduced-motion .animate-spin,
.reduced-motion .animate-pulse,
.reduced-motion .animate-bounce,
.reduced-motion .animate-float {
  animation: none !important;
}

/* 触摸和手势优化 */
.touch-optimized {
  touch-action: manipulation;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.swipe-container {
  touch-action: pan-x;
  overflow-x: hidden;
}

.swipe-item {
  touch-action: manipulation;
  transition: transform 0.3s ease;
}

.swipe-item:active {
  transform: scale(0.98);
}

/* 长按效果 */
.long-press {
  transition: all 0.2s ease;
}

.long-press:active {
  transform: scale(0.95);
  filter: brightness(0.9);
}

/* 拖拽指示器 */
.drag-handle {
  cursor: grab;
  touch-action: none;
}

.drag-handle:active {
  cursor: grabbing;
}

/* 滑动删除效果 */
.swipe-to-delete {
  position: relative;
  overflow: hidden;
}

.swipe-to-delete::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  width: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 59, 48, 0.8));
  transition: width 0.3s ease;
  pointer-events: none;
}

.swipe-to-delete.swiping::after {
  width: 100%;
}

/* 下拉刷新效果 */
.pull-to-refresh {
  position: relative;
  overflow: hidden;
}

.pull-to-refresh::before {
  content: '';
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 30px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.pull-to-refresh.refreshing::before {
  opacity: 1;
}

/* 触觉反馈模拟 */
@keyframes haptic-light {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

@keyframes haptic-medium {
  0% { transform: scale(1); }
  25% { transform: scale(1.03); }
  75% { transform: scale(0.98); }
  100% { transform: scale(1); }
}

@keyframes haptic-heavy {
  0% { transform: scale(1); }
  20% { transform: scale(1.05); }
  40% { transform: scale(0.95); }
  60% { transform: scale(1.02); }
  80% { transform: scale(0.98); }
  100% { transform: scale(1); }
}

.haptic-light {
  animation: haptic-light 0.1s ease;
}

.haptic-medium {
  animation: haptic-medium 0.2s ease;
}

.haptic-heavy {
  animation: haptic-heavy 0.3s ease;
}
