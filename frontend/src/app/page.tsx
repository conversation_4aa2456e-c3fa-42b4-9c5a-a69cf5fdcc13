"use client"

import * as React from "react"
import { AnimatePresence, motion } from "framer-motion"
import { <PERSON>ap, LogIn, UserPlus } from "lucide-react"
import { Background } from "@/components/ui/background"
import { GlassCard, GlassButton } from "@/components/ui/glass"
import { EnhancedGlassCard } from "@/components/ui/enhanced-glass"
import { <PERSON>adeIn, <PERSON>agger, Floating } from "@/components/ui/animated-elements"
import { PageTransition } from "@/components/ui/page-transitions"
import { Ma<PERSON>Button, Typewriter, CountUp } from "@/components/ui/micro-interactions"
import { Modal } from "@/components/ui/modal"
import { LoginForm } from "@/components/forms/login-form"
import { RegisterForm } from "@/components/forms/register-form"
import { BindForm } from "@/components/forms/bind-form"
import { UserPanel } from "@/components/user/user-panel"
import type { User, OrderGroup } from "@/types"

type ViewType = 'initial' | 'login' | 'register' | 'bind' | 'userPanel'

export default function Home() {
  const [currentView, setCurrentView] = React.useState<ViewType>('initial')
  const [user, setUser] = React.useState<User | null>(null)
  const [orders, setOrders] = React.useState<OrderGroup[]>([])
  const [isLoading, setIsLoading] = React.useState(false)
  const [error, setError] = React.useState<string | null>(null)
  const [isLoadingOrders, setIsLoadingOrders] = React.useState(false)
  const [showOrderDetails, setShowOrderDetails] = React.useState(false)
  const [selectedOrderGroup, setSelectedOrderGroup] = React.useState<OrderGroup | null>(null)

  // 检查登录状态
  React.useEffect(() => {
    const savedUser = localStorage.getItem('user')
    if (savedUser) {
      try {
        const userData = JSON.parse(savedUser)
        if (userData && userData.token) {
          setUser(userData)
          setCurrentView('userPanel')
          fetchUserOrders(userData.token)
        }
      } catch (error) {
        console.error('解析用户数据失败:', error)
        localStorage.removeItem('user')
      }
    }
  }, [])

  // 获取用户订单
  const fetchUserOrders = async (token?: string) => {
    const userToken = token || user?.token
    if (!userToken) return

    setIsLoadingOrders(true)
    try {
      const response = await fetch('/user/bindings', {
        headers: {
          'Authorization': `Bearer ${userToken}`,
          'Content-Type': 'application/json'
        }
      })

      const result = await response.json()
      if (result.success) {
        // 处理订单数据，按群号分组
        const groupedOrders: { [key: string]: OrderGroup } = {}

        result.data?.forEach((order: any) => {
          if (!groupedOrders[order.groupNumber]) {
            groupedOrders[order.groupNumber] = {
              groupNumber: order.groupNumber,
              items: [],
              skuTypes: new Set(),
              totalDays: 0,
              finalExpirationDate: null,
              hasPermanent: false
            }
          }

          groupedOrders[order.groupNumber].items.push(order)
          groupedOrders[order.groupNumber].skuTypes.add(order.skuType)

          // 检查是否有永久订单
          if (order.expirationDate.includes('永久')) {
            groupedOrders[order.groupNumber].hasPermanent = true
          } else {
            // 计算剩余天数
            try {
              const expirationDate = new Date(order.expirationDate)
              const now = new Date()
              if (expirationDate > now) {
                const diffTime = expirationDate.getTime() - now.getTime()
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
                groupedOrders[order.groupNumber].totalDays += diffDays
              }
            } catch (e) {
              console.error("计算日期错误:", e)
            }
          }
        })

        setOrders(Object.values(groupedOrders))
      } else {
        throw new Error(result.message || '获取订单失败')
      }
    } catch (error) {
      console.error('获取订单失败:', error)
    } finally {
      setIsLoadingOrders(false)
    }
  }

  // 登录处理
  const handleLogin = async (credentials: { username: string; password: string }) => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/user/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(credentials)
      })

      const result = await response.json()

      if (result.success && result.data) {
        const userData = result.data
        setUser(userData)
        localStorage.setItem('user', JSON.stringify(userData))
        setCurrentView('userPanel')
        await fetchUserOrders(userData.token)
      } else {
        throw new Error(result.message || '登录失败')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '登录失败')
    } finally {
      setIsLoading(false)
    }
  }

  // 注册处理
  const handleRegister = async (data: {
    username: string
    email: string
    password: string
    confirmPassword: string
  }) => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/user/register', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
      })

      const result = await response.json()

      if (result.success) {
        // 注册成功后自动登录
        await handleLogin({ username: data.username, password: data.password })
      } else {
        throw new Error(result.message || '注册失败')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '注册失败')
    } finally {
      setIsLoading(false)
    }
  }

  // 绑定订单处理
  const handleBind = async (data: { orderNumber: string; groupNumber: string }) => {
    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch('/user/bind', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': user?.token ? `Bearer ${user.token}` : ''
        },
        body: JSON.stringify(data)
      })

      const result = await response.json()

      if (result.success) {
        if (user) {
          setCurrentView('userPanel')
          await fetchUserOrders()
        } else {
          setCurrentView('initial')
        }
      } else {
        throw new Error(result.message || '绑定失败')
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '绑定失败')
    } finally {
      setIsLoading(false)
    }
  }

  // 退出登录
  const handleLogout = () => {
    setUser(null)
    setOrders([])
    localStorage.removeItem('user')
    setCurrentView('initial')
  }

  // 查看订单详情
  const handleViewOrderDetails = (group: OrderGroup) => {
    setSelectedOrderGroup(group)
    setShowOrderDetails(true)
  }

  return (
    <PageTransition variant="spring">
      <Background variant="user">
        <div className="min-h-screen p-4 flex items-center justify-center">
        <div className="w-full max-w-lg mx-auto">
          {/* 头部标题 */}
          <FadeIn delay={0.2} className="text-center mb-8">
            <Floating intensity="subtle" speed="slow">
              <div className="inline-flex items-center justify-center w-16 h-16 glass-strong rounded-2xl mb-4 shadow-2xl shadow-primary/20">
                <Zap className="w-8 h-8 text-white" />
              </div>
            </Floating>
            <motion.h1
              className="text-4xl font-bold text-white mb-2 bg-gradient-to-r from-white via-white to-white/80 bg-clip-text"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.4, duration: 0.6 }}
            >
              <Typewriter
                text="ww小岸"
                speed={150}
                delay={400}
                cursor={false}
              />
            </motion.h1>
            <motion.p
              className="text-white/80 text-lg font-medium"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.2, duration: 0.6 }}
            >
              <Typewriter
                text="自助服务中心"
                speed={100}
                delay={1200}
                cursor={false}
              />
            </motion.p>
          </FadeIn>

          {/* 主要内容区域 */}
          <AnimatePresence mode="wait">
            {currentView === 'initial' && (
              <InitialView
                onShowLogin={() => setCurrentView('login')}
                onShowRegister={() => setCurrentView('register')}
              />
            )}

            {currentView === 'login' && (
              <LoginForm
                onSubmit={handleLogin}
                onBack={() => setCurrentView('initial')}
                onSwitchToRegister={() => setCurrentView('register')}
                isLoading={isLoading}
                error={error}
              />
            )}

            {currentView === 'register' && (
              <RegisterForm
                onSubmit={handleRegister}
                onBack={() => setCurrentView('initial')}
                onSwitchToLogin={() => setCurrentView('login')}
                isLoading={isLoading}
                error={error}
              />
            )}

            {currentView === 'bind' && (
              <BindForm
                onSubmit={handleBind}
                onBack={() => user ? setCurrentView('userPanel') : setCurrentView('initial')}
                isLoading={isLoading}
                error={error}
              />
            )}

            {currentView === 'userPanel' && user && (
              <UserPanel
                user={user}
                orders={orders}
                isLoadingOrders={isLoadingOrders}
                onLogout={handleLogout}
                onRefreshOrders={() => fetchUserOrders()}
                onBindNewOrder={() => setCurrentView('bind')}
                onViewOrderDetails={handleViewOrderDetails}
              />
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* 订单详情模态框 */}
      <Modal
        isOpen={showOrderDetails}
        onClose={() => setShowOrderDetails(false)}
        title={selectedOrderGroup ? `群 ${selectedOrderGroup.groupNumber} 的订单详情` : '订单详情'}
        size="lg"
      >
        {selectedOrderGroup && (
          <OrderDetailsContent group={selectedOrderGroup} />
        )}
      </Modal>
    </Background>
  )
}

// 初始视图组件
interface InitialViewProps {
  onShowLogin: () => void
  onShowRegister: () => void
}

function InitialView({ onShowLogin, onShowRegister }: InitialViewProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <EnhancedGlassCard
        variant="aurora"
        size="lg"
        glow="subtle"
        floating
        shimmer
        className="w-full max-w-lg mx-auto"
      >
        <FadeIn delay={0.1} className="text-center mb-8">
          <h2 className="text-2xl font-semibold text-white mb-3 bg-gradient-to-r from-white to-white/90 bg-clip-text">
            欢迎使用
          </h2>
          <p className="text-white/70">请选择您要进行的操作</p>
        </FadeIn>

        <Stagger staggerDelay={0.15} className="space-y-4">
          <MagneticButton
            onClick={onShowLogin}
            className="w-full"
            strength={0.2}
          >
            <GlassButton
              variant="strong"
              size="lg"
              className="w-full group relative overflow-hidden pointer-events-none"
              leftIcon={
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-xl flex items-center justify-center group-hover:from-blue-500/30 group-hover:to-purple-500/30 transition-all duration-300 group-hover:scale-110">
                  <LogIn className="w-5 h-5 transition-transform duration-300 group-hover:scale-110" />
                </div>
              }
            >
              <span className="text-lg font-medium">登录账户</span>
            </GlassButton>
          </MagneticButton>

          <MagneticButton
            onClick={onShowRegister}
            className="w-full"
            strength={0.2}
          >
            <GlassButton
              variant="strong"
              size="lg"
              className="w-full group relative overflow-hidden pointer-events-none"
              leftIcon={
                <div className="w-10 h-10 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-xl flex items-center justify-center group-hover:from-green-500/30 group-hover:to-emerald-500/30 transition-all duration-300 group-hover:scale-110">
                  <UserPlus className="w-5 h-5 transition-transform duration-300 group-hover:scale-110" />
                </div>
              }
            >
              <span className="text-lg font-medium">注册账户</span>
            </GlassButton>
          </MagneticButton>
        </Stagger>
      </EnhancedGlassCard>
    </motion.div>
  )
}

// 订单详情内容组件
interface OrderDetailsContentProps {
  group: OrderGroup
}

function OrderDetailsContent({ group }: OrderDetailsContentProps) {
  const groupAvatarUrl = `https://p.qlogo.cn/gh/${group.groupNumber}/${group.groupNumber}/100`

  return (
    <div className="space-y-6">
      {/* 群组信息 */}
      <div className="flex items-center pb-4 border-b border-white/20">
        <img
          src={groupAvatarUrl}
          alt="群头像"
          className="h-14 w-14 rounded-2xl object-cover shadow-lg border-2 border-white/20"
          onError={(e) => {
            e.currentTarget.src = 'https://placehold.co/56x56/667eea/ffffff?text=群'
          }}
        />
        <div className="ml-4">
          <h4 className="font-medium text-white text-lg">群号: {group.groupNumber}</h4>
          <p className="text-white/70">共 {group.items.length} 个订单</p>
        </div>
      </div>

      {/* 订单列表 */}
      <div className="space-y-3">
        {group.items.map((item, index) => {
          // 计算单个订单的剩余天数
          let itemRemainingDaysText = ''
          let itemExpirationClass = ''

          if (item.expirationDate.includes('永久')) {
            itemRemainingDaysText = '永久'
            itemExpirationClass = 'bg-green-500/20 text-green-200 border-green-500/30'
          } else {
            try {
              const expirationDate = new Date(item.expirationDate)
              const now = new Date()
              const diffTime = expirationDate.getTime() - now.getTime()
              const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

              if (diffDays <= 0) {
                itemRemainingDaysText = '已过期'
                itemExpirationClass = 'bg-red-500/20 text-red-200 border-red-500/30'
              } else {
                itemRemainingDaysText = `${diffDays}天`
                itemExpirationClass = 'bg-green-500/20 text-green-200 border-green-500/30'
              }
            } catch (e) {
              itemRemainingDaysText = '无法计算'
              itemExpirationClass = 'bg-gray-500/20 text-gray-200 border-gray-500/30'
            }
          }

          return (
            <div key={`${item.orderNumber}-${item.skuId}`} className="glass rounded-2xl p-4">
              <div className="flex justify-between items-start mb-3">
                <div>
                  <h5 className="font-medium text-white">订单号: {item.orderNumber}</h5>
                  {item.isActive && (
                    <span className="inline-block mt-1 px-2 py-0.5 text-xs rounded-xl bg-yellow-500/20 text-yellow-200 border border-yellow-500/30">
                      激活中
                    </span>
                  )}
                </div>
                <span className={`px-2 py-0.5 rounded-xl text-xs font-medium border ${itemExpirationClass}`}>
                  {itemRemainingDaysText}
                </span>
              </div>

              <div className="grid grid-cols-2 gap-2 text-sm text-white/80">
                <div>
                  <span className="text-white/60">档位:</span>{' '}
                  <span className="font-medium">{item.skuType}</span>
                </div>
                <div>
                  <span className="text-white/60">绑定时间:</span>{' '}
                  <span className="font-medium">
                    {item.bindTime ? new Date(item.bindTime).toLocaleDateString() : '-'}
                  </span>
                </div>
                <div>
                  <span className="text-white/60">到期时间:</span>{' '}
                  <span className="font-medium">{item.expirationDate}</span>
                </div>
                <div>
                  <span className="text-white/60">所有者:</span>{' '}
                  <span className="font-medium">{item.owner || '您'}</span>
                </div>
              </div>
            </div>
          )
        })}
        </div>
      </Background>
    </PageTransition>
  )
}
