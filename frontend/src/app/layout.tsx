import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import PerformanceMonitor from "@/components/ui/performance-monitor";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "ww小岸 - 现代化付费管理系统",
  description: "基于Next.js + TypeScript + Tailwind CSS构建的现代化付费管理系统，采用苹果风格设计和玻璃拟态效果",
  keywords: ["付费管理", "订单管理", "现代化界面", "玻璃拟态", "苹果风格"],
  authors: [{ name: "ww小岸团队" }],
  viewport: "width=device-width, initial-scale=1",
  themeColor: "#007aff",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased font-sans`}
      >
        {children}
        <PerformanceMonitor />
      </body>
    </html>
  );
}
